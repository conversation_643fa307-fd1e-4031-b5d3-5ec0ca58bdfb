<?php
// Enqueue the parent and child theme's stylesheets
function child_enqueue_styles()
{
    // Enqueue parent theme's stylesheet
    wp_enqueue_style('parent-style', get_template_directory_uri() . '/style.css');

    // Enqueue child theme's stylesheet
    wp_enqueue_style('child-style', get_stylesheet_uri(), array('parent-style'));

    if (is_checkout()) {
        wp_enqueue_style('checkout-style', get_stylesheet_directory_uri() . '/assets/css/checkout.css', array());
        wp_enqueue_style('product-options', get_stylesheet_directory_uri() . '/assets/css/product-options.css', array());
        wp_enqueue_style('flag-icons', 'https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.2.3/css/flag-icons.min.css', array(), '7.2.3');
        wp_dequeue_style('woocommerce-general-inline-css');
        wp_enqueue_script('product-options', get_stylesheet_directory_uri() . '/assets/js/product-options.js', array('jquery'), null, true);
        wp_enqueue_script('country-flags', get_stylesheet_directory_uri() . '/assets/js/country-flags.js', array('jquery'), '1.0.0', true);
        wp_enqueue_script('checkout-quantity', get_stylesheet_directory_uri() . '/assets/js/checkout-quantity.js', array('jquery', 'wc-checkout'), '1.0.0', true);
    }
    if (is_cart()) {
        wp_enqueue_style('cart-style', get_stylesheet_directory_uri() . '/assets/css/cart.css', array('woocommerce-general'));
    }
    if (has_shortcode(get_post()->post_content, 'woocommerce_order_tracking')) {
        wp_enqueue_style('order-tracking-style', get_stylesheet_directory_uri() . '/assets/css/order-trackings.css', array());
    }

}

add_action('wp_enqueue_scripts', 'child_enqueue_styles', 999);

// Move payment methods to separate section
function create_custom_payment_section() {
    // Remove payment from order review
    remove_action('woocommerce_checkout_order_review', 'woocommerce_checkout_payment', 20);
    
    // Add payment to the separate payment section
    add_action('woocommerce_checkout_payment_section', 'woocommerce_checkout_payment', 10);
}
add_action('init', 'create_custom_payment_section');

// Function to map custom order status slugs to labels and descriptions
function custom_order_status_label($status)
{
    $custom_labels = array(
        'wc-cm_completed_forz' => array(
            'title' => 'Completed - Check Your Email',
            'description' => 'Your order was successfully completed, please check your email inbox / spam folder for additional information. Thank you for purchasing!'
        ),
        'wc-cm_started_genera' => array(
            'title' => 'Order Started - CriminalModz',
            'description' => 'We would like to inform that a seller/supplier has started working on your order! Please remain logged off the account until the order is completed.'
        ),
        'wc-cm_completed_gene' => array(
            'title' => 'Completed - Check Your Email',
            'description' => 'Your order was successfully completed, please check your email inbox / spam folder for additional information. Thank you for purchasing!'
        ),
        'wc-ea_no_webapp' => array(
            'title' => 'Issue - No Transfermarket',
            'description' => 'Your order could not be processed, as transfermarket is not unlocked on your web app. Please check your email for more information.'
        ),
        'wc-ea_wrong_login' => array(
            'title' => 'Issue - Wrong Login',
            'description' => 'Your order could not be processed, as you provided us with the wrong EA login credentials. Please check your email for more information.'
        ),
        'wc-ea_wrong_codes' => array(
            'title' => 'Issue - Wrong Backup Codes',
            'description' => 'Your order could not be processed, as you provided us with the wrong 2FA backup codes. Please check your email for more information.'
        ),
        'wc-microsoft_2fa_on' => array(
            'title' => 'Issue - 2FA Enabled',
            'description' => 'Your order could not be processed, as 2-Step Verification is enabled on your account. Please check your email for more information.'
        ),
        'wc-microsoft_wrong_l' => array(
            'title' => 'Issue - Wrong Login',
            'description' => 'Your order could not be processed, as the microsoft login credentials you provided are invalid. Please check your email for more information.'
        ),
        'wc-psn_wrong_codes' => array(
            'title' => 'Issue - Wrong Backup Codes',
            'description' => 'Your order could not be processed, as you provided us with the wrong PSN backup codes. Please check your email for more information.'
        ),
        'wc-psn_wrong_login' => array(
            'title' => 'Issue - Wrong Login',
            'description' => 'Your order could not be processed, as you provided us with the invalid PSN login credentials. Please check your email for more information.'
        ),
        'wc-steam_guard_on' => array(
            'title' => 'Issue - Steam Guard On',
            'description' => 'Your order could not be processed, as the steam guard is enabled. Please check your email for more information.'
        ),
        'wc-steam_wrong_login' => array(
            'title' => 'Issue - Wrong Login',
            'description' => 'Your order could not be processed, as you provided us with the invalid steam username or password. Please check your email for more information.'
        ),
        'wc-cm_order_correcte' => array(
            'title' => 'Order Corrected',
            'description' => 'Your order was previously invalid, but it has now been corrected. We will attempt to do the order again and we will inform you of any updates.'
        ),
        'wc-cm_game_missing' => array(
            'title' => 'Game Missing',
            'description' => 'Your order could not be processed, as the account you provided does not have the game purchased.'
        ),
        'wc-cm_assigned_iboos' => array(
            'title' => 'Order Assigned to: Booster 01',
            'description' => 'Your order has been assigend to CriminalModzCOD Booster 01. Check yor email for the contact details.'
        ),
        'wc-cm_assigned_boost' => array(
            'title' => 'Order Assigned to: Booster 02',
            'description' => 'Your order has been assigend to CriminalModzCOD Booster 02. Check yor email for the contact details.'
        ),
        'wc-cm_lobbies_no_ans' => array(
            'title' => 'Order Halted - No Answer',
            'description' => 'Your order has been halted because we did not receive a response to you on social media. Please check your email for details.'
        ),
        'wc-cm_fn_completed' => array(
            'title' => 'Order Completed',
            'description' => 'Your fortnite order has been completed. We have sent out the account credentials to your email address.'
        ),
        'wc-cm_activision_wro' => array(
            'title' => 'Order Halted - Invalid Username',
            'description' => 'Your order has been halted because the activision ID you provided is invalid. Please respond back to our emails with the correct one.'
        )
        // Add more mappings as needed
    );

    if (isset($custom_labels[$status])) {
        return $custom_labels[$status];
    } else {
        return array(
            'title' => wc_get_order_status_name(str_replace('wc-', '', $status)),
            'description' => ''
        );
    }
}

// Hook to change the order status name globally
function custom_wc_order_status_name($status, $order)
{
    $status_info = custom_order_status_label('wc-' . $status);
    return $status_info['title'];
}

add_filter('woocommerce_order_status_name', 'custom_wc_order_status_name', 10, 2);

// Additional hook for order status display in tracking pages and emails
function custom_wc_order_status_display($status)
{
    $status_info = custom_order_status_label($status);
    return $status_info['title'];
}

add_filter('woocommerce_order_status', 'custom_wc_order_status_display', 10, 1);

function pvd($var)
{
    echo '<pre>';
    var_dump($var);
    echo '</pre>';
}

include('hooks/back-btn.php');

include('hooks/product_options/render_option.php');
include('hooks/product_options/render_button.php');
include('hooks/product_options/render_checkbox.php');
include('hooks/product_options/render_selector.php');
include('hooks/product_options/render_number.php');
include('hooks/product_options/render_text.php');

include_once('woo_helper.php');
include_once('classes/woocommerce/Woo_Checkout.php');
include_once('classes/woocommerce/Product_Customizations.php');

function custom_admin_styles()
{
    echo '<style>' . file_get_contents(get_stylesheet_directory_uri() . '/assets/css/admin-styles.css') . '</style>';
}

add_action('admin_head', 'custom_admin_styles');
function custom_admin_scripts()
{
    wp_enqueue_script('custom-admin-js', get_stylesheet_directory_uri() . '/assets/js/admin-scripts.js', array('jquery'), null, true);
}

add_action('acf/input/admin_enqueue_scripts', 'custom_admin_scripts');

// Register the webhook receiver endpoint
add_action('rest_api_init', function () {
    register_rest_route('nergetix-webhook/v1', '/update-order-status', [
        'methods' => 'POST',
        'callback' => 'handle_webhook_request',
        'permission_callback' => '__return_true',
    ]);
});

function handle_webhook_request(WP_REST_Request $request)
{
    // Get the parameters from the request
    $order_id = $request->get_param('id');
    $order_status = $request->get_param('status');

    // Validate the order ID and order status
    if (empty($order_id) || empty($order_status)) {
        return new WP_REST_Response(['message' => 'Invalid parameters'], 400);
    }

    // Load the WooCommerce order
    $order = wc_get_order($order_id);

    if (!$order) {
        return new WP_REST_Response(['message' => 'Order not found'], 404);
    }

    // Update the order status
    try {
        $order->update_status($order_status, 'Status updated via webhook', true);
        return new WP_REST_Response(['message' => 'Order status updated successfully'], 200);
    } catch (Exception $e) {
        return new WP_REST_Response(['message' => 'Failed to update order status: ' . $e->getMessage()], 500);
    }
}


/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Adding custom fields to the order edit page (email and password)
// Attention: CSS code added to the file: assets/css/admin-styles.css
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// Add custom fields to the order edit page
add_action('woocommerce_admin_order_data_after_order_details', 'add_custom_order_meta_fields');

function add_custom_order_meta_fields($order)
{
    echo '<div class="custom-order-meta-wrapper">';
    woocommerce_wp_text_input(array(
        'id' => 'order_metadata_emailepic',
        'label' => __('Epic Games E-mail', 'woocommerce'),
        'description' => __('Enter the Epic Games email for this order.', 'woocommerce'),
        'value' => get_post_meta($order->get_id(), 'order_metadata_emailepic', true),
        'wrapper_class' => 'form-field-wide'
    ));

    woocommerce_wp_text_input(array(
        'id' => 'order_metadata_passwordepic',
        'label' => __('Epic Games Password:', 'woocommerce'),
        'description' => __('Enter the Epic Games password for this order.', 'woocommerce'),
        'value' => get_post_meta($order->get_id(), 'order_metadata_passwordepic', true),
        'wrapper_class' => 'form-field-wide'
    ));

    woocommerce_wp_text_input(array(
        'id' => 'order_metadata_mailpass',
        'label' => __('Email Password', 'woocommerce'),
        'description' => __('Enter the password for Email access.', 'woocommerce'),
        'value' => get_post_meta($order->get_id(), 'order_metadata_mailpass', true),
        'wrapper_class' => 'form-field-wide'
    ));
    woocommerce_wp_text_input(array(
        'id' => 'order_metadata_notes',
        'label' => __('Other Notes', 'woocommerce'),
        'description' => __('Enter any other notes.', 'woocommerce'),
        'value' => get_post_meta($order->get_id(), 'order_metadata_notes', true),
        'wrapper_class' => 'form-field-wide'
    ));
    woocommerce_wp_text_input(array(
        'id' => 'order_metadata_microsoftmail',
        'label' => __('Microsoft E-mail', 'woocommerce'),
        'description' => __('Enter the Microsoft E-Mail for this order.', 'woocommerce'),
        'value' => get_post_meta($order->get_id(), 'order_metadata_microsoftmail', true),
        'wrapper_class' => 'form-field-wide'
    ));
    woocommerce_wp_text_input(array(
        'id' => 'order_metadata_steamusername',
        'label' => __('Steam Username', 'woocommerce'),
        'description' => __('Enter the Steam Username for this order.', 'woocommerce'),
        'value' => get_post_meta($order->get_id(), 'order_metadata_steamusername', true),
        'wrapper_class' => 'form-field-wide'
    ));
    woocommerce_wp_text_input(array(
        'id' => 'order_metadata_microsoftpass',
        'label' => __('Microsoft Password', 'woocommerce'),
        'description' => __('Enter the Microsoft Password for this order.', 'woocommerce'),
        'value' => get_post_meta($order->get_id(), 'order_metadata_microsoftpass', true),
        'wrapper_class' => 'form-field-wide'
    ));
    woocommerce_wp_text_input(array(
        'id' => 'order_metadata_steampass',
        'label' => __('Steam Password', 'woocommerce'),
        'description' => __('Enter the Setam Password for this order.', 'woocommerce'),
        'value' => get_post_meta($order->get_id(), 'order_metadata_steampass', true),
        'wrapper_class' => 'form-field-wide'
    ));
    woocommerce_wp_text_input(array(
        'id' => 'order_metadata_psnemail',
        'label' => __('PSN E-Mail', 'woocommerce'),
        'description' => __('Enter the PSN E-Mail for this order.', 'woocommerce'),
        'value' => get_post_meta($order->get_id(), 'order_metadata_psnmail', true),
        'wrapper_class' => 'form-field-wide'
    ));
    woocommerce_wp_text_input(array(
        'id' => 'order_metadata_psnpass',
        'label' => __('PSN Password', 'woocommerce'),
        'description' => __('Enter the PSN Password for this order.', 'woocommerce'),
        'value' => get_post_meta($order->get_id(), 'order_metadata_psnpass', true),
        'wrapper_class' => 'form-field-wide'
    ));
    woocommerce_wp_text_input(array(
        'id' => 'order_metadata_psncodes',
        'label' => __('PSN Codes', 'woocommerce'),
        'description' => __('Enter the PSN Codes for this order.', 'woocommerce'),
        'value' => get_post_meta($order->get_id(), 'order_metadata_psncodes', true),
        'wrapper_class' => 'form-field-wide'
    ));
    echo '</div>';
}

// Save custom fields
add_action('woocommerce_process_shop_order_meta', 'save_custom_order_meta_fields');

function save_custom_order_meta_fields($post_id)
{
    $order_metadata_emailepic = isset($_POST['order_metadata_emailepic']) ? sanitize_text_field($_POST['order_metadata_emailepic']) : '';
    update_post_meta($post_id, 'order_metadata_emailepic', $order_metadata_emailepic);

    $order_metadata_passwordepic = isset($_POST['order_metadata_passwordepic']) ? sanitize_text_field($_POST['order_metadata_passwordepic']) : '';
    update_post_meta($post_id, 'order_metadata_passwordepic', $order_metadata_passwordepic);

    $order_metadata_mailpass = isset($_POST['order_metadata_mailpass']) ? sanitize_text_field($_POST['order_metadata_mailpass']) : '';
    update_post_meta($post_id, 'order_metadata_mailpass', $order_metadata_mailpass);

    $order_metadata_notes = isset($_POST['order_metadata_notes']) ? sanitize_text_field($_POST['order_metadata_notes']) : '';
    update_post_meta($post_id, 'order_metadata_notes', $order_metadata_notes);

    $order_metadata_microsoftmail = isset($_POST['order_metadata_microsoftmail']) ? sanitize_text_field($_POST['order_metadata_microsoftmail']) : '';
    update_post_meta($post_id, 'order_metadata_microsoftmail', $order_metadata_microsoftmail);

    $order_metadata_steamusername = isset($_POST['order_metadata_steamusername']) ? sanitize_text_field($_POST['order_metadata_steamusername']) : '';
    update_post_meta($post_id, 'order_metadata_steamusername', $order_metadata_steamusername);

    $order_metadata_microsoftpass = isset($_POST['order_metadata_microsoftpass']) ? sanitize_text_field($_POST['order_metadata_microsoftpass']) : '';
    update_post_meta($post_id, 'order_metadata_microsoftpass', $order_metadata_microsoftpass);

    $order_metadata_steampass = isset($_POST['order_metadata_steampass']) ? sanitize_text_field($_POST['order_metadata_steampass']) : '';
    update_post_meta($post_id, 'order_metadata_steampass', $order_metadata_steampass);
    
    $order_metadata_psnemail = isset($_POST['order_metadata_psnemail']) ? sanitize_text_field($_POST['order_metadata_psnemail']) : '';
    update_post_meta($post_id, 'order_metadata_psnemail', $order_metadata_psnemail);

    $order_metadata_psnpass = isset($_POST['order_metadata_psnpass']) ? sanitize_text_field($_POST['order_metadata_psnpass']) : '';
    update_post_meta($post_id, 'order_metadata_psnpass', $order_metadata_psnpass);

    $order_metadata_psncodes = isset($_POST['order_metadata_psncodes']) ? sanitize_text_field($_POST['order_metadata_psncodes']) : '';
    update_post_meta($post_id, 'order_metadata_psncodes', $order_metadata_psncodes);
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////
// create a custom shortcode to display the order email and password in email templates
// usage: [order_email] and [order_password]
//////////////////////////////////////////////////////////////////////////////////////////////////////////////

add_shortcode('metadata_emailepic', function ($atts) {
    if (
        function_exists('alg_wc_custom_emails') &&
        !empty(alg_wc_custom_emails()->core->shortcodes->order)
    ) {
        $order = alg_wc_custom_emails()->core->shortcodes->order;
        $order_metadata_emailepic = get_post_meta($order->ID, 'order_metadata_emailepic', true);
        return $order_metadata_emailepic;
    } else {
        return '';
    }
});

add_shortcode('metadata_passwordepic', function ($atts) {
    if (
        function_exists('alg_wc_custom_emails') &&
        !empty(alg_wc_custom_emails()->core->shortcodes->order)
    ) {
        $order = alg_wc_custom_emails()->core->shortcodes->order;
        $order_metadata_passwordepic = get_post_meta($order->ID, 'order_metadata_passwordepic', true);
        return $order_metadata_passwordepic;
    } else {
        return '';
    }
});

add_shortcode('metadata_mailpass', function ($atts) {
    if (
        function_exists('alg_wc_custom_emails') &&
        !empty(alg_wc_custom_emails()->core->shortcodes->order)
    ) {
        $order = alg_wc_custom_emails()->core->shortcodes->order;
        $order_metadata_mailpass = get_post_meta($order->ID, 'order_metadata_mailpass', true);
        return $order_metadata_mailpass;
    } else {
        return '';
    }
});

add_shortcode('metadata_notes', function ($atts) {
    if (
        function_exists('alg_wc_custom_emails') &&
        !empty(alg_wc_custom_emails()->core->shortcodes->order)
    ) {
        $order = alg_wc_custom_emails()->core->shortcodes->order;
        $order_metadata_notes = get_post_meta($order->ID, 'order_metadata_notes', true);
        return $order_metadata_notes;
    } else {
        return '';
    }
});

add_shortcode('metadata_microsoftmail', function ($atts) {
    if (
        function_exists('alg_wc_custom_emails') &&
        !empty(alg_wc_custom_emails()->core->shortcodes->order)
    ) {
        $order = alg_wc_custom_emails()->core->shortcodes->order;
        $order_metadata_microsoftmail = get_post_meta($order->ID, 'order_metadata_microsoftmail', true);
        return $order_metadata_microsoftmail;
    } else {
        return '';
    }
});

add_shortcode('metadata_steamusername', function ($atts) {
    if (
        function_exists('alg_wc_custom_emails') &&
        !empty(alg_wc_custom_emails()->core->shortcodes->order)
    ) {
        $order = alg_wc_custom_emails()->core->shortcodes->order;
        $order_metadata_steamusername = get_post_meta($order->ID, 'order_metadata_steamusername', true);
        return $order_metadata_steamusername;
    } else {
        return '';
    }
});

add_shortcode('metadata_microsoftpass', function ($atts) {
    if (
        function_exists('alg_wc_custom_emails') &&
        !empty(alg_wc_custom_emails()->core->shortcodes->order)
    ) {
        $order = alg_wc_custom_emails()->core->shortcodes->order;
        $order_metadata_microsoftpass = get_post_meta($order->ID, 'order_metadata_microsoftpass', true);
        return $order_metadata_microsoftpass;
    } else {
        return '';
    }
});

add_shortcode('metadata_steampass', function ($atts) {
    if (
        function_exists('alg_wc_custom_emails') &&
        !empty(alg_wc_custom_emails()->core->shortcodes->order)
    ) {
        $order = alg_wc_custom_emails()->core->shortcodes->order;
        $order_metadata_steampass = get_post_meta($order->ID, 'order_metadata_steampass', true);
        return $order_metadata_steampass;
    } else {
        return '';
    }
});

add_shortcode('metadata_psnemail', function ($atts) {
    if (
        function_exists('alg_wc_custom_emails') &&
        !empty(alg_wc_custom_emails()->core->shortcodes->order)
    ) {
        $order = alg_wc_custom_emails()->core->shortcodes->order;
        $order_metadata_psnemail = get_post_meta($order->ID, 'order_metadata_psnemail', true);
        return $order_metadata_psnemail;
    } else {
        return '';
    }
});

add_shortcode('metadata_psnpass', function ($atts) {
    if (
        function_exists('alg_wc_custom_emails') &&
        !empty(alg_wc_custom_emails()->core->shortcodes->order)
    ) {
        $order = alg_wc_custom_emails()->core->shortcodes->order;
        $order_metadata_psnpass = get_post_meta($order->ID, 'order_metadata_psnpass', true);
        return $order_metadata_psnpass;
    } else {
        return '';
    }
});

add_shortcode('metadata_psncodes', function ($atts) {
    if (
        function_exists('alg_wc_custom_emails') &&
        !empty(alg_wc_custom_emails()->core->shortcodes->order)
    ) {
        $order = alg_wc_custom_emails()->core->shortcodes->order;
        $order_metadata_psncodes = get_post_meta($order->ID, 'order_metadata_psncodes', true);
        return $order_metadata_psncodes;
    } else {
        return '';
    }
});


//////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Clear the cart before adding any product
//////////////////////////////////////////////////////////////////////////////////////////////////////////////

add_filter('woocommerce_add_to_cart_validation', 'clear_cart_and_add_single_product', 10, 3);
function clear_cart_and_add_single_product($passed, $product_id, $quantity)
{
    if (!WC()->cart->is_empty()) {
        WC()->cart->empty_cart();
    }
    return $passed;
}

add_filter( 'elementor_pro/custom_fonts/font_display', function( $current_value, $font_family, $data ) {
    return 'swap';
}, 10, 3 );

add_filter( 'xmlrpc_enabled', '__return_false' );

//////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Allow all file types to be uploaded
//////////////////////////////////////////////////////////////////////////////////////////////////////////////
#
# For this, see: wp-includes/capabilities.php > map_meta_cap()
#
function wpse_6533_map_unrestricted_upload_filter($caps, $cap) {
    if ($cap == 'unfiltered_upload') {
        $caps = array();
        $caps[] = $cap;
    }

    return $caps;
}

add_filter('map_meta_cap', 'wpse_6533_map_unrestricted_upload_filter', 0, 2);


//////////////////////////////////////////////////////////////////////////////////////////////////////////////
// redirect the cart and checkout pages to the checkout page
//////////////////////////////////////////////////////////////////////////////////////////////////////////////

add_action( 'woocommerce_init', 'so_77294197_replace_cart_url' );
function so_77294197_replace_cart_url() {
    add_filter( 'woocommerce_add_to_cart_redirect', function() {
        return wc_get_checkout_url();
    });

    add_filter( 'woocommerce_get_cart_url', function() {
        return wc_get_checkout_url();
    });
}


//////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Set the default product price based on the smallest cost in the repeater field
//////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
//add_filter( 'woocommerce_product_get_price', 'set_product_price_based_on_acf', 20, 2 );
//add_filter( 'woocommerce_product_get_regular_price', 'set_product_price_based_on_acf', 20, 2 );
//
//function set_product_price_based_on_acf( $price, $product ) {
//    // Get the product ID.
//    $product_id = $product->get_id();
//
//    // Check if the product already has a price.  If it does, do nothing.
//    if ( $price > 0.01 ) {
//        return $price;
//    }
//
//    // Get the repeater field data.
//    $product_options = get_field( 'product_options', $product_id );
//
//    // Check if the repeater field has any rows.
//    if ( is_array( $product_options ) && ! empty( $product_options ) ) {
//
//        $smallest_cost = null;
//        $cost_found = false;
//
//        // Iterate through each row in the 'product_options' repeater.
//        foreach ( $product_options as $option_group ) {
//            if ( isset( $option_group['product_option'] ) ) {
//                $product_option = $option_group['product_option'];
//
//                // Check for 'cost' in 'values'
//                if ( isset( $product_option['values'] ) && is_array( $product_option['values'] ) ) {
//                    foreach ( $product_option['values'] as $value_item ) {
//                        if ( isset( $value_item['cost'] ) && is_numeric( $value_item['cost'] ) ) {
//                            $cost = floatval( $value_item['cost'] );
//                            $cost_found = true;
//                            if ( $smallest_cost === null || $cost < $smallest_cost ) {
//                                $smallest_cost = $cost;
//                            }
//                        }
//                    }
//                }
//
//                // Check for 'cost' in 'discount_bundle'
//                if ( isset( $product_option['slug'] ) && $product_option['slug'] === 'discount_bundle' && isset( $product_option['values'] ) && is_array( $product_option['values'] )) {
//                    foreach ( $product_option['values'] as $value_item) {
//                        if (isset($value_item['cost']) && is_numeric($value_item['cost'])) {
//                            $cost = floatval($value_item['cost']);
//                            $cost_found = true;
//                            if ($smallest_cost === null || $cost < $smallest_cost) {
//                                $smallest_cost = $cost;
//                            }
//                        }
//                    }
//                }
//            }
//        }
//
//        // If we found a valid cost, set the product price.
//        if ( $smallest_cost !== null ) {
//            $price = $smallest_cost;
//        } elseif ( ! $cost_found ) {
//            //  left as a place to log and debug if needed later
//        }
//    }
//
//    return $price;
//}

function enqueue_google_fonts() {
    wp_enqueue_style('google-fonts-manrope', 'https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700&display=swap', false);
}
add_action('wp_enqueue_scripts', 'enqueue_google_fonts');

// debug output of ACF fields for the product
//add_action( 'woocommerce_before_single_product', 'display_acf_fields_for_debugging' );
//
//function display_acf_fields_for_debugging() {
//    global $product;
//
//    if ( ! $product ) {
//        return;
//    }
//
//    $product_id = $product->get_id();
//
//    // Get all ACF fields for the product.
//    $acf_fields = get_fields( $product_id );
//
//    if ( $acf_fields ) {
//        echo '<div style="border: 1px solid #eee; padding: 10px; margin-bottom: 20px; background-color: #f8f8f8; overflow-x: auto;">';
//            echo '<h3>ACF Fields for Product ID: ' . $product_id . '</h3>';
//            echo '<pre>';
//                print_r( $acf_fields ); // Use print_r for better readability of arrays.
//            echo '</pre>';
//        echo '</div>';
//    } else {
//        echo '<div style="border: 1px solid #eee; padding: 10px; margin-bottom: 20px; background-color: #f8f8f8; max-width: 500px; overflow-x: auto;">';
//            echo '<h3>ACF Fields for Product ID: ' . $product_id . '</h3>';
//            echo '<p>No ACF fields found for this product.</p>';
//        echo '</div>';
//    }
//}



require_once("woocommerce/multi_currency.php");
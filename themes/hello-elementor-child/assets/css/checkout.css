@font-face {
    font-family: '<PERSON><PERSON><PERSON>';
    src: url('../fonts/BrittiSansTrial-Regular-BF6757bfd47ffbf.otf') format('opentype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

body.woocommerce-checkout article>header{
    display:none;
}
.woocommerce-form-coupon-toggle{
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    font-size: 14px;
    line-height: 21px;
    background: #F6F8FB;
    border-radius: 6px;
    margin-left: 48px;
    padding: 10px 12px;
}
.woocommerce-form-coupon-toggle a{
    display: inline-block;
    margin-left: 4px;
}
.woocommerce-form-coupon-toggle svg{
    display: inline-block;
    margin-right: 8px;
}
.woocommerce form.checkout_coupon{
    padding: 20px 45px 0px;
}
.woocommerce form.checkout_coupon p{
    width: 100%;
}
.woocommerce form.checkout_coupon p button{
    background: #3C4257;
    -webkit-box-shadow: 0px -1px 1px rgba(0, 0, 0, 0.12), 0px 2px 5px rgba(0, 0, 0, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.08);
    box-shadow: 0px -1px 1px rgba(0, 0, 0, 0.12), 0px 2px 5px rgba(0, 0, 0, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.08);
    border-radius: 6px;
    line-height: 24px;
    font-weight: 500;
    font-size: 15px;
    width: 100%;
    transition: 0.3s ease;
    margin-top: 15px;
}
.woocommerce form.checkout_coupon p button:hover{
    background: #3c4257c4;
    transition: 0.3s ease;
}
.woocommerce form.checkout_coupon p:first-child{
    color: rgba(26, 26, 26, 0.7);
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 5px;
}
body.woocommerce-checkout form.checkout::after{
    display: none !important;
}
body.woocommerce-checkout form.checkout{
    display: block;
    width: 100%;
}

.checkout-main-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    width: 1034px;
    max-width: 1034px;
    gap: 30px;
    margin: 0 auto;
}

.checkout-column-1 {
    width: 516px;
    max-width: 516px;
    flex-shrink: 0;
}

.checkout-column-2 {
    width: 488px;
    max-width: 488px;
    flex-shrink: 0;
}

body.woocommerce-checkout form.checkout .copyright-text{
    color: #ACACAC;
    font-size: 14px;
    line-height: 20px;
}
body.woocommerce-checkout form.checkout .copyright-text-bottom{
    display: none;
}
body.woocommerce-checkout form.checkout>div.woocommerce-NoticeGroup{
    width: 100%;
    max-width: 100%;
}

body.woocommerce-checkout form.checkout #customer_details{
    width: 100%;
    max-width: 100%;
    flex-shrink: 0;
}

body.woocommerce-checkout form.checkout #order_review{
    width: 100%;
    max-width: 100%;
    flex-shrink: 0;
}

body.woocommerce-checkout form.checkout #payment{
    width: 100%;
    max-width: 100%;
    order: unset;
    margin-top: 0px;
}
body.woocommerce-checkout form.checkout .form-fields-title{
    margin: 0;
    padding: 0;
    margin-top: 32px;
    margin-bottom: 24px;
    border-bottom: none;
}
body.woocommerce-checkout form.checkout .form-fields-title label{
    color: #3C4257;
    font-weight: 600;
    font-size: 18px;
    line-height: 24px;
    margin: 0px;
}
.woocommerce-js form .form-row label{
    font-family: 'Britti Sans', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: rgba(26, 26, 26, 0.7);
    line-height: 17px;
    margin-bottom: 7px;
    padding-bottom: 7px;
}
.woocommerce-js form .form-row input.input-text, .woocommerce-js form .form-row textarea{
    font-family: 'Britti Sans', sans-serif;
    font-weight: 400;
    background: #FFFFFF;
    padding: 10px 12px;
    -webkit-box-shadow: 0px 0px 0px 1px #e0e0e038, 0px 2px 4px rgba(0, 0, 0, 0.07), 0px 1px 1.5px rgb(255 255 255 / 5%);
    box-shadow: 0px 0px 0px 1px #e0e0e038, 0px 2px 4px rgba(0, 0, 0, 0.07), 0px 1px 1.5px rgb(255 255 255 / 5%);
    border-radius: 6px;
}

.woocommerce-js form .form-row input.input-text::-webkit-input-placeholder, .woocommerce-js form .form-row textarea::-webkit-input-placeholder{
    color: rgba(26, 26, 26, 0.5);
    font-family: 'Manrope', sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 19px;
}

.woocommerce-js form .form-row input.input-text::-moz-placeholder, .woocommerce-js form .form-row textarea::-moz-placeholder{
    color: rgba(26, 26, 26, 0.5);
    font-family: 'Manrope', sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 19px;
}

.woocommerce-js form .form-row input.input-text:-ms-input-placeholder, .woocommerce-js form .form-row textarea:-ms-input-placeholder{
    color: rgba(26, 26, 26, 0.5);
    font-family: 'Manrope', sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 19px;
}

.woocommerce-js form .form-row input.input-text::-ms-input-placeholder, .woocommerce-js form .form-row textarea::-ms-input-placeholder{
    color: rgba(26, 26, 26, 0.5);
    font-family: 'Manrope', sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 19px;
}

.woocommerce-js form .form-row input.input-text::placeholder,
.woocommerce-js form .form-row textarea::placeholder{
    color: rgba(26, 26, 26, 0.5);
    font-family: 'Manrope', sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 19px;
}
.woocommerce-js form .form-row{
    padding: 0px;
    margin: 0px 0px 0px 0px;
}
.woocommerce-js form .form-row span.description{
    font-size: 12px;
}
p+#billing_heading_field{
    border-top: 1px solid rgba(60, 66, 87, 0.12);
    padding-top: 32px;
	clear:both;
}
#billing_country_field{
    margin: 0px;
}
.woocommerce-js .select2-container .select2-selection--single, .woocommerce-js select, .woocommerce-page .select2-container .select2-selection--single, .woocommerce-page select{
    max-height: 40px;
    border-radius: 8px;
}
#billing_postcode_field,
#billing_city_field{
    width: calc(50% - 15px);
}
#billing_city_field{
    margin-right: 30px;
}
#billing_city_field input{
    border-radius: 8px;
}
#billing_postcode_field input{
    border-radius: 8px;
}
.woocommerce-js .select2-container--default .select2-selection--single .select2-selection__rendered{
    line-height: 15px;
}
#order_review h3{
    color: #3C4257;
    font-weight: 600;
    font-size: 18px;
    line-height: 24px;
    margin: 0;
    padding: 0;
    margin-top: 32px;
    margin-bottom: 24px;
    border-bottom: none;
}

#order_review{
    background: #FFFFFF;
    border-radius: 12px;
    padding: 18px 23px 25px 23px;
    box-shadow: 
        0px 0px 1px rgba(0, 0, 0, 0.12),
        0px 2px 5px rgba(103, 110, 118, 0.08);
    position: sticky;
    top: 20px;
    height: auto;
}

#order_review .top-total{
    font-weight: 600;
    font-size: 36px;
    line-height: 44px;
    letter-spacing: -0.02em;
    padding: 3px 0px 12px 0px;
    border-bottom: none;
    text-align: left !important;
    color: #1A1F36;
}
#order_review .top-total .woocommerce-Price-amount{
    font-weight: 600;
    font-size: 36px;
    line-height: 44px;
    letter-spacing: -0.02em;
    color: #1A1F36;
}

#order_review{
    padding: 0px;
}
.woocommerce-page.woocommerce-checkout form #order_review td, .woocommerce-page.woocommerce-checkout form #order_review th, .woocommerce.woocommerce-checkout form #order_review td, .woocommerce.woocommerce-checkout form #order_review th{
    border: none;
}
.woocommerce-page.woocommerce-checkout form #order_review td.product-name, .woocommerce.woocommerce-checkout form #order_review td.product-name{
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
}
.woocommerce-page.woocommerce-checkout form #order_review td .name{
    font-size: 14px;
    line-height: 20px;
    font-weight: 600;
    color: #1A1F36;
}
.woocommerce-page.woocommerce-checkout form #order_review td .product-categories{
    padding-right: 25px;
}
.woocommerce-page.woocommerce-checkout form #order_review td .product-categories a{
    font-size: 14px;
    line-height: 20px;
    pointer-events: none;
    color: #697386;
}
.woocommerce-page.woocommerce-checkout #payment div.form-row, .woocommerce.woocommerce-checkout #payment div.form-row{
    margin-bottom: 0px;
}
#order_review .woocommerce-Price-amount{
    color: #1A1F36;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
}
.woocommerce-page.woocommerce-checkout form #order_review td:last-child, .woocommerce-page.woocommerce-checkout form #order_review th:last-child, .woocommerce.woocommerce-checkout form #order_review td:last-child, .woocommerce.woocommerce-checkout form #order_review th:last-child{
    vertical-align: top;
}
#order_review .shop_table tbody tr:last-child .product-name{
    padding-bottom: 12px;
}
#order_review .cart-subtotal th{
    border-top: 1px solid rgba(60, 66, 87, 0.12) !important;
    padding: 8px;
}
#order_review .cart-subtotal td{
    border-top: 1px solid rgba(60, 66, 87, 0.12) !important;
    padding: 8px 0px;
}
#order_review tfoot tr th{
    font-size: 14px;
    line-height: 20px;
    font-weight: 600;
    color: #1A1F36;
}
#payment .wc_payment_methods li .payment_box{
    display: none !important;
}
#order_review .tax-total th,
#order_review .tax-total td{
    padding: 0px 0px 8px 0px;
}
#order_review .tax-total th{
    color: #697386;
}
.woocommerce-page.woocommerce-checkout table.shop_table td, .woocommerce.woocommerce-checkout table.shop_table td{
    padding: 8px 0px;
}
#order_review .order-total th,
#order_review .order-total td{
    border-top: 1px solid rgba(60, 66, 87, 0.12) !important;
    padding-bottom: 0px !important;
}

/* New checkout order review styles */
.checkout-order-review {
    background: #FFFFFF;
    border-radius: 12px;
    padding: 23px 20px;
    box-shadow:
        0px 0px 1px rgba(0, 0, 0, 0.12),
        0px 2px 5px rgba(103, 110, 118, 0.08);
}

.product-card {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    border-bottom: 1px solid rgba(60, 66, 87, 0.12);
    margin-bottom: 24px;
}

.product-card:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
}

.product-card .product-image {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
    background: #F8F9FA;
}

.product-card .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-card .product-details {
    flex: 1;
    min-width: 0;
}

.product-card .product-title {
    font-family: 'Britti Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    color: #454C52;
    margin-bottom: 8px;
}

.product-card .product-description {
    font-family: 'Britti Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: auto;
    color: #9EA5AD;
    margin-bottom: 12px;
}

.product-card .product-description ul {
    margin: 0;
    padding-left: 16px;
}

.product-card .product-description li {
    margin-bottom: 4px;
}

.product-card .product-price {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: #1A1F36;
}



.coupon-section {
    margin-bottom: 32px;
}

.coupon-form-container {
    display: flex;
    gap: 12px;
    align-items: center;
}

.coupon-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid rgba(60, 66, 87, 0.12);
    border-radius: 8px;
    font-size: 14px;
    line-height: 20px;
    color: #1A1F36;
    background: #FFFFFF;
}

.coupon-input::placeholder {
    color: #697386;
}

.coupon-apply-btn {
    padding: 12px 20px;
    background: #DC3545;
    border: none;
    border-radius: 8px;
    color: #FFFFFF;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.coupon-apply-btn:hover {
    background: #C82333;
}

.order-totals {
    margin-bottom: 32px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(60, 66, 87, 0.08);
}

.total-row:last-child {
    border-bottom: none;
}

.coupon-row .total-value {
    display: flex;
    align-items: center;
    gap: 8px;
}

.coupon-row .woocommerce-remove-coupon {
    color: #DC3545;
    text-decoration: none;
    font-size: 12px;
    padding: 2px 6px;
    border: 1px solid #DC3545;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.coupon-row .woocommerce-remove-coupon:hover {
    background: #DC3545;
    color: #FFFFFF;
}

.total-label {
    font-size: 14px;
    line-height: 20px;
    color: #697386;
}

.total-value {
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: #1A1F36;
}

.final-total-row {
    padding: 20px 0 0 0;
    border-top: 1px solid rgba(60, 66, 87, 0.12);
    margin-top: 16px;
}

.final-total-row .total-label {
    font-weight: 600;
    font-size: 18px;
    line-height: 24px;
    color: #1A1F36;
}

.final-total-row .total-currency {
    font-size: 14px;
    line-height: 20px;
    color: #697386;
    margin-left: auto;
    margin-right: 8px;
}

.final-total-row .total-value {
    font-weight: 600;
    font-size: 24px;
    line-height: 32px;
    color: #1A1F36;
}

.pay-now-section {
    margin-top: 32px;
}

.pay-now-btn {
    width: 100%;
    padding: 16px 24px;
    background: #DC3545;
    border: none;
    border-radius: 8px;
    color: #FFFFFF;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.pay-now-btn:hover {
    background: #C82333;
    color: #FFFFFF;
}

.pay-now-btn svg {
    width: 16px;
    height: 16px;
}

/* Hide old table-based order review */
#order_review .shop_table.woocommerce-checkout-review-order-table {
    display: none;
}

#order_review .form-row.place-order {
    display: none;
}

/* Override existing order review styles */
#order_review {
    background: transparent;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
    position: static;
}

/* Responsive design */
@media (max-width: 768px) {
    .product-card {
        flex-direction: column;
        gap: 12px;
    }

    .product-card .product-image {
        width: 100%;
        height: 200px;
        align-self: center;
        max-width: 200px;
    }

    .coupon-form-container {
        flex-direction: column;
        gap: 12px;
    }

    .coupon-apply-btn {
        width: 100%;
        justify-content: center;
    }

    .final-total-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .final-total-row .total-currency {
        margin: 0;
    }
}
#payment .wc_payment_methods li{
    background: #FFFFFF;
    border: 1px solid rgba(60, 66, 87, 0.12);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px !important;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    cursor: pointer;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    width: 100%;
}
#payment .wc_payment_methods li label{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    font-size: 16px;
    line-height: 22px;
    color: #1A1F36;
    font-weight: 600;
    width: 100%;
}
#payment .wc_payment_methods li .payment_box{
    width: 100%;
    margin-bottom: 0px;
}
#payment .wc_payment_methods li img{
    max-height: 24px;
}
#payment .wc_payment_methods li .input_box{
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.wc_payment_methods li {
    display: flex;
    transition: 0.3s ease;
}

.wc_payment_methods li:hover{
    -webkit-box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.08), 0px 1px 1px rgba(0, 0, 0, 0.04);
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.08), 0px 1px 1px rgba(0, 0, 0, 0.04);
    transition: 0.3s ease;
}

/* Hide payment methods beyond the first 4 when collapsed - High specificity */
#payment .wc_payment_methods li:nth-child(n+5),
#payment ul.payment_methods li:nth-child(n+5),
.woocommerce-checkout #payment ul.payment_methods li:nth-child(n+5) {
    display: none !important;
}

/* Show all payment methods when expanded */
.see-all-active #payment .wc_payment_methods li:nth-child(n+5),
.see-all-active #payment ul.payment_methods li:nth-child(n+5),
.see-all-active .woocommerce-checkout #payment ul.payment_methods li:nth-child(n+5) {
    display: flex !important;
}
.woocommerce-page.woocommerce-checkout #payment ul.payment_methods, .woocommerce.woocommerce-checkout #payment ul.payment_methods{
    margin: 0px;
    padding-bottom: 0px;
}
.see-more-block {
    position: absolute;
    bottom: 5px;
    right: 20px;
    z-index: 10;
}
.see-more-payments {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25px;
    height: 18px;
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: 0.3s ease;
}
.see-more-payments svg{
    width: 25px;
    height: 18px;
    transition: 0.3s ease;
    transform: rotate(180deg);
}
.see-all-active .see-more-payments svg{
    transform: rotate(0deg);
    transition: 0.3s ease;
}
.see-more-payments:hover svg{
    transform: rotate(180deg) scale(1.1);
    transition: 0.3s ease;
}
.see-all-active .see-more-payments:hover svg{
    transform: rotate(0deg) scale(1.1);
    transition: 0.3s ease;
}
.see-more-payments span{
    display: none;
}
#payment .place-order{
    display: none !important;
}

/* Hide place order button in payment section to prevent duplication */
.payment_section #place_order,
.payment_section .place-order,
.woocommerce-checkout-payment #place_order,
.woocommerce-checkout-payment .place-order {
    display: none !important;
}
/* Payment button styles removed since button is hidden */

/* Place order button in order summary */
#order_review .place-order{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding: 20px 0 0 0;
}
#order_review .place-order button{
    width: 100%;
    background: #C52233;
    -webkit-box-shadow: 0px -1px 1px rgba(0, 0, 0, 0.12), 0px 2px 5px rgba(0, 0, 0, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.08);
    box-shadow: 0px -1px 1px rgba(0, 0, 0, 0.12), 0px 2px 5px rgba(0, 0, 0, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.08);
    border-radius: 6px;
    font-size: 16px;
    transition: 0.3s ease;
    line-height: 24px;
    font-weight: 500;
    padding: 12px 20px;
    border: none;
    color: #FFFFFF;
    cursor: pointer;
    font-family: 'Britti Sans', sans-serif;
}
#order_review .place-order button:hover{
    background: #A01A2A;
    transition: 0.3s ease;
}
#order_review .place-order .woocommerce-terms-and-conditions-wrapper{
    margin-bottom: 16px;
}
#order_review .place-order .woocommerce-privacy-policy-text p,
#order_review .place-order .woocommerce-terms-and-conditions-checkbox-text{
    color: #8792A2;
    font-size: 14px;
    line-height: 20px;
    margin: 0 0 10px 0;
}
.woocommerce-remove-coupon{
    font-size: 12px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-results__option--highlighted[data-selected]{
    background: #3C4257;
}
body.woocommerce-checkout .woocommerce-notices-wrapper .woocommerce-message{
    display: none;
}
h3.wcccf_field{
    color: #3C4257;
    font-weight: 600;
    font-size: 18px;
    line-height: 24px;
    padding: 0;
    margin-top: 32px !important;
    margin-bottom: 24px;
    border-bottom: none;
    clear: both;
}
p + h3.wcccf_field{
    border-top: 1px solid rgba(60, 66, 87, 0.12);
    padding-top: 32px !important;
}
.woocommerce-order{
    max-width: 574px;
    margin: auto;
}
.woocommerce-order .paymen-success{
    text-align: center;
}
.woocommerce-order .paymen-success__title{
    color: #3C4257;
    font-size: 36px;
    line-height: 44px;
    font-weight: 600;
    margin: 16px 0px;
}
.woocommerce-thankyou-order-received{
    text-align: center;
    color: #697386;
    font-size: 14px;
    line-height: 22px;
}
.woocommerce-order-overview{
    padding: 0px !important;
    margin: 0px !important;
}
.woocommerce-order-overview li{
    color: #697386;
    font-size: 14px !important;
    font-weight: 500;
    line-height: 22px !important;
    padding-right: 20px !important;
    margin-right: 20px !important;
}
.woocommerce-order-overview li:last-child{
    padding-right: 0px !important;
    margin-right: 0px !important;
}
.woocommerce-order-overview li strong{
    font-size: 14px !important;
    line-height: 22px !important;
    color: #3C4257;
    margin-top: 8px;
}
.woocommerce-order .woocommerce-order-details{
    margin-top: 48px;
    background: #FFFFFF;
    border: 1px solid rgba(60, 66, 87, 0.12) !important;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.08), 0px 1px 1px rgba(0, 0, 0, 0.04);
    border-radius: 8px !important;
    overflow: hidden;
}
.woocommerce-order .woocommerce-order-details .woocommerce-order-details__title{
    color: #3C4257;
    font-size: 18px !important;
    line-height: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    background: #F6F8FB !important;
    border-radius: 8px 8px 0px 0px;
    padding: 12px 16px !important;
}
.woocommerce-order .woocommerce-order-details thead tr th:first-child{
    border-right-width: 1px !important;
}
.woocommerce-order .woocommerce-order-details thead tr th{
    color: #3C4257;
}
.woocommerce-order .woocommerce-order-details tr td a{
    color: #635BFF;
}
.woocommerce-order .woocommerce-order-details thead tr td{
    color: #697386;
}
.woocommerce-order .woocommerce-order-details tr th,
.woocommerce-order .woocommerce-order-details tr td,
.woocommerce-order .woocommerce-order-details tr td a{
    font-size: 14px;
    line-height: 17px;
}
.woocommerce-order .woocommerce-order-details tfoot th{
    color: #697386;
}
.woocommerce-js .woocommerce .woocommerce-order table.shop_table{
    margin-bottom: 0px !important;
    border-radius: 0px 0px 8px 8px;
}

.checkout-step-body{
    display: none;
}
.checkout-step-body.active{
    display: block;
}
#customer_details .checkout-steps{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 48px;
}
#customer_details .checkout-steps .checkout-step{
    width: 50%;
    color: rgba(60, 66, 87, 0.5);
    text-align: center;
    font-weight: 400;
    font-size: 14px;
    padding-bottom: 16px;
    line-height: 19px;
    text-transform: uppercase;
    transition: 0.3s ease;
    border-bottom: 2px solid #F6F8FB;
    cursor: pointer;
}
#customer_details .checkout-steps .checkout-step:hover{
    color: #000;
    transition: 0.3s ease;
}
#customer_details .checkout-steps .checkout-step.active{
    color: #000;
    border-bottom: 2px solid #000;
    font-weight: 600;
    transition: 0.3s ease;
}
.checkout-step-body h3.wcccf_field{
    margin-top: 0px !important;
}
.woocommerce-js form .form-row select{
    font-size: 14px;
}
.checkout-step-body .next-step,
.checkout-step-body .prev-step{
    margin-top: 25px;
    text-align: right;
}
.checkout-step-body .prev-step{
    text-align: left;
}
.checkout-step-body .next-step button,
.checkout-step-body .prev-step button{
    background: #3C4257;
    transition: 0.3s ease;
}
.checkout-step-body .next-step button:hover,
.checkout-step-body .prev-step button:hover{
    background: #3c4257c4;
    transition: 0.3s ease;
}
#order_review_heading{
    position: relative;
}
#order_review_heading .product-image{
    display: block;
    max-width: 150px;
    position: absolute;
    right: 0;
    top: 0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0px 0px 0px 1px #E0E0E0, 0px 2px 4px rgba(0, 0, 0, 0.07), 0px 1px 1.5px rgba(0, 0, 0, 0.05);
    max-height: 100px;
    width: auto;
}
@media (max-width: 1200px){
    body.woocommerce-checkout #main{
        margin-top: 50px;
    }
}
@media (max-width: 990px){
    .checkout-main-container {
        flex-direction: column;
        width: 100%;
        max-width: 100%;
    }
    .checkout-column-1,
    .checkout-column-2 {
        width: 100%;
        max-width: 100%;
    }
    body.woocommerce-checkout .payment_section {
        width: 100%;
        max-width: 100%;
    }
    .woocommerce form.checkout_coupon{
        width: 100%;
    }
    .copyright-text{
        display: none;
    }
    #order_review{
        -webkit-box-shadow: none;
        box-shadow: none;
        border: none;
        position: static;
    }
    body.woocommerce-checkout form.checkout .copyright-text{
        display: none;
    }
    body.woocommerce-checkout form.checkout .copyright-text-bottom{
        display: block;
        text-align: center;
        margin-bottom: 25px;
    }
    body.woocommerce-checkout form.checkout #customer_details{
        margin-bottom: 0px;
    }
    #order_review_heading{
        margin-top: 0px !important;
    }
}
@media (max-width: 590px){
    .woocommerce .back-button .btn-action{
        margin-left: 15px;
    }
    .woocommerce form.checkout_coupon{
        padding: 20px 0px 0px;
    }
    #customer_details, #order_review{
        padding: 0px;
    }
    .woocommerce-form-coupon-toggle{
        margin: 0px;
    }
    body.woocommerce-checkout #main{
        margin-top: 25px;
    }
    .woocommerce-js ul.order_details li{
        width: 100%;
        border: none !important;
        margin-bottom: 16px;
    }
    .woocommerce-js ul.order_details li:last-child{
        margin-bottom: 0px;
    }
}
@media (max-width: 620px){
    #customer_details .checkout-steps{
        margin-top: 25px;
    }
}
@media (max-width: 480px){
    .woocommerce-form-coupon-toggle{
        font-size: 13px;
    }
    .woocommerce form .form-row-first, .woocommerce form .form-row-last, .woocommerce-page form .form-row-first, .woocommerce-page form .form-row-last{
        width: 100%;
    }
}

/* Custom Checkout Top Bar */
.checkout-top-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #FFFFFF;
    height: 66px;
    border-bottom: 1px solid #E5E7EA;
    z-index: 9999;
    display: flex;
    align-items: center;
}

.checkout-top-bar-inner {
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    padding: 19px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-bar-left {
    display: flex;
    align-items: center;
    margin-left: 56px;
}

.back-arrow {
    display: flex;
    align-items: center;
    margin-right: 8px;
}

.back-arrow svg {
    width: 16px;
    height: 16px;
}

.checkout-logo {
    display: flex;
    align-items: center;
}

.checkout-logo img {
    width: 164px;
    height: auto;
}

.top-bar-right {
    display: flex;
    align-items: center;
    margin-right: 80px;
}

.dark-mode-text {
    font-size: 14px;
    color: #3C4257;
    margin-right: 10px;
}

.dark-mode-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.dark-mode-toggle svg {
    width: 70px;
    height: 42px;
}

/* Adjust main content to account for fixed header */
body.woocommerce-checkout {
    padding-top: 66px;
}

/* Hide back button since we have it in the top bar */
body.woocommerce-checkout .back-button {
    display: none !important;
}

/* Billing Information Container Styling */
body.woocommerce-checkout form.checkout {
    margin-top: 88px;
    max-width: 1034px;
    margin-left: auto;
    margin-right: auto;
}

body.woocommerce-checkout form.checkout .billing_section {
    background: #FFFFFF;
    border-radius: 12px;
    padding: 18px 23px 25px 23px;
    box-shadow:
        0px 0px 1px rgba(0, 0, 0, 0.12),
        0px 2px 5px rgba(103, 110, 118, 0.08);
}

/* Billing Information Typography */
body.woocommerce-checkout form.checkout .form-fields-title {
    font-family: 'Britti Sans', sans-serif;
    font-weight: 500;
    font-size: 20px;
    line-height: 20px;
    color: #1A1A1A;
    margin: 0 0 14px 0;
    text-transform: none;
    letter-spacing: 0%;
    text-align: left;
}

body.woocommerce-checkout form.checkout .form-fields-title label {
    font-family: 'Britti Sans', sans-serif;
    font-weight: 500;
    font-size: 20px;
    line-height: 20px;
    color: #1A1A1A;
}

/* Dark mode styles */
body.dark-mode .checkout-top-bar {
    background: #1A1A1A;
    border-bottom-color: #333333;
}

body.dark-mode .dark-mode-text {
    color: #FFFFFF;
}

body.dark-mode.woocommerce-checkout form.checkout .billing_section {
    background: #2A2A2A;
    box-shadow: 
        0px 0px 1px rgba(255, 255, 255, 0.12),
        0px 2px 5px rgba(0, 0, 0, 0.3);
}

body.dark-mode.woocommerce-checkout form.checkout .form-fields-title,
body.dark-mode.woocommerce-checkout form.checkout .form-fields-title label {
    color: #FFFFFF;
}

/* Payment Methods Container Styling */
body.woocommerce-checkout .payment_section {
    background: #FFFFFF;
    border-radius: 12px;
    padding: 18px 23px 25px 23px;
    box-shadow:
        0px 0px 1px rgba(0, 0, 0, 0.12),
        0px 2px 5px rgba(103, 110, 118, 0.08);
    margin-bottom: 20px;
    margin-top: 34px;
    width: 516px;
    max-width: 516px;
    position: relative;
}

body.woocommerce-checkout #payment.woocommerce-checkout-payment {
    background: transparent;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
    margin: 0;
}

/* Payment Method Title Typography */
body.woocommerce-checkout #payment.woocommerce-checkout-payment h3 {
    font-family: 'Britti Sans', sans-serif;
    font-weight: 500;
    font-size: 20px;
    line-height: 20px;
    color: #1A1A1A;
    margin: 0 0 14px 0;
    text-transform: none;
    letter-spacing: 0%;
    text-align: left;
}

/* Dark mode styles for payment section */
body.dark-mode.woocommerce-checkout .payment_section {
    background: #2A2A2A;
    box-shadow: 
        0px 0px 1px rgba(255, 255, 255, 0.12),
        0px 2px 5px rgba(0, 0, 0, 0.3);
}

body.dark-mode.woocommerce-checkout #payment.woocommerce-checkout-payment {
    background: transparent;
    box-shadow: none;
}

body.dark-mode.woocommerce-checkout #payment.woocommerce-checkout-payment h3 {
    color: #FFFFFF;
}

/* Dark mode styles for order review */
body.dark-mode #order_review {
    background: #2A2A2A;
    box-shadow: 
        0px 0px 1px rgba(255, 255, 255, 0.12),
        0px 2px 5px rgba(0, 0, 0, 0.3);
}

/* Dark mode styles for pay button in order review */
body.dark-mode #order_review .place-order button{
    background: #C52233;
    color: #FFFFFF;
}
body.dark-mode #order_review .place-order button:hover{
    background: #A01A2A;
}
body.dark-mode #order_review .place-order .woocommerce-privacy-policy-text p,
body.dark-mode #order_review .place-order .woocommerce-terms-and-conditions-checkbox-text{
    color: #8792A2;
}

/* Coupon Form Styling in Order Review */
.coupon-row {
    border-top: none;
    border-bottom: none;
}

.coupon-form-container {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 0;
    border-bottom: 1px solid #E5E7EA;
}

.coupon-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #E5E7EA;
    border-radius: 6px;
    font-size: 14px;
    color: #3C4257;
    background: #F8F9FA;
}

.coupon-input::placeholder {
    color: #8792A2;
}

.coupon-apply-btn {
    background: #C52233;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: background 0.2s ease;
}

.coupon-apply-btn:hover {
    background: #A01A2A;
}

.coupon-apply-btn svg {
    width: 12px;
    height: 12px;
}

/* Dark mode styles for coupon */
body.dark-mode .coupon-form-container {
    border-bottom-color: #333333;
}

body.dark-mode .coupon-input {
    background: #2A2A2A;
    border-color: #333333;
    color: #FFFFFF;
}

body.dark-mode .coupon-input::placeholder {
    color: #8792A2;
}

.woocommerce-checkout #content {
    margin-top: 0px;
}

body.woocommerce-checkout {
    padding-top: 0px;
}

.woocommerce-checkout .woocommerce {
padding-top:0px;
}

.woocommerce-billing-fields__field-wrapper .form-row {
margin-top:0px!important;
}

#customer_details > div.billing_section > div > div > h3 {
padding-bottom:10px;
 border-bottom:1px solid #E5E7EA;
  margin-bottom:8px;
}

.woocommerce-billing-fields__field-wrapper label {
padding-top:15px;
}

/* Override screen-reader-text class to make labels visible */
.woocommerce-billing-fields__field-wrapper label.screen-reader-text {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: 15px 0 7px 0 !important;
    margin: 0 !important;
    overflow: visible !important;
    clip: auto !important;
    clip-path: none !important;
    border: 0 !important;
    color: rgba(26, 26, 26, 0.7) !important;
    background: transparent !important;
    font-family: 'Britti Sans', sans-serif !important;
    font-weight: 400 !important;
    font-size: 14px !important;
    line-height: 17px !important;
    display: block !important;
}

.required {
 display:none;
}

.woocommerce-billing-fields__field-wrapper input{
border-radius:8px!important;
}

/* Country flags styling */
.select2-container--default .select2-results__option {
    padding: 8px 12px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    padding-left: 12px;
    padding-right: 20px;
    line-height: 38px;
    transition: opacity 0.1s ease-in-out;
}

.select2-container--default .select2-selection--single {
    height: 40px;
    border: 1px solid #e0e0e038;
    border-radius: 8px;
    box-shadow: 0px 0px 0px 1px #e0e0e038, 0px 2px 4px rgba(0, 0, 0, 0.07), 0px 1px 1.5px rgb(255 255 255 / 5%);
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 38px;
    right: 12px;
}

.select2-dropdown {
    border: 1px solid #e0e0e038;
    border-radius: 8px;
    box-shadow: 0px 0px 0px 1px #e0e0e038, 0px 2px 4px rgba(0, 0, 0, 0.07), 0px 1px 1.5px rgb(255 255 255 / 5%);
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #f8f9fa;
    color: #333;
}

/* Flag icon sizing */
.fi {
    width: 20px !important;
    height: 20px !important;
    background-size: cover !important;
    border-radius: 100px;
    display: inline-block;
    vertical-align: middle;
}

/* ROBUST PAYMENT METHODS STYLING - ELIMINATING CONFLICTS */

/* Reset payment methods container */
.woocommerce-checkout #payment ul.payment_methods {
    padding: 0px !important;
    margin: 0px !important;
}

.woocommerce-checkout #payment ul.payment_methods::before {
    content: " ";
    display: none !important;
}

/* Payment methods layout - CSS Grid with 4 columns - High specificity */
#payment .wc_payment_methods,
#payment ul.wc_payment_methods,
#payment ul.payment_methods,
.woocommerce-checkout #payment ul.payment_methods {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 20px 23px !important;
    overflow: hidden !important;
    height: 65px !important;
    transition: height 0.4s ease, opacity 0.3s ease !important;
    padding: 0 !important;
    margin: 0 !important;
    list-style: none !important;
}

/* When expanded, allow auto height */
.see-all-active #payment .wc_payment_methods,
.see-all-active #payment ul.wc_payment_methods,
.see-all-active #payment ul.payment_methods,
.see-all-active .woocommerce-checkout #payment ul.payment_methods {
    height: auto !important;
}

/* Individual payment method styling - override existing settings */
.wc_payment_method {
    width: 100% !important;
    height: 43px !important;
    object-fit: contain !important;
    margin: 0 !important;
    display: flex !important;
    background: #FFFFFF !important;
    border: 0px solid transparent !important;
    border-radius: 6px !important;
    padding: 0px !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.2s ease !important;
}

/* Hide radio buttons completely */
.wc_payment_method input[type="radio"] {
    position: absolute !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    pointer-events: none !important;
}

/* Make the entire payment method clickable */
.wc_payment_method .input_box {
    background: transparent !important;
    transition: all 0.2s ease !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    border: none !important;
    padding: 0px !important;
    margin: 0px !important;
}

/* Make label clickable and full size */
.wc_payment_method .input_box label {
    cursor: pointer !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    line-height: 0px !important;
    margin: 0px !important;
    padding: 0px !important;
    font-size: 0px !important;
    color: transparent !important;
}

/* Checked states - robust selectors */
.wc_payment_method input[type="radio"]:checked ~ .input_box,
.wc_payment_method input[type="radio"]:checked + .input_box,
.wc_payment_method:has(input[type="radio"]:checked) .input_box,
.wc_payment_method:has(input[type="radio"]:checked) {
    border: 1px solid #C52233 !important;
    border-radius: 6px !important;
    background: #FFFFFF !important;
}

.wc_payment_method input[type="radio"]:checked ~ .input_box:hover,
.wc_payment_method input[type="radio"]:checked + .input_box:hover,
.wc_payment_method:has(input[type="radio"]:checked):hover {
    border: 1px solid #C52233 !important;
    border-radius: 6px !important;
    background: #FFFFFF !important;
}

/* Hide text labels while keeping icons visible */
.wc_payment_method .input_box label {
    text-indent: -9999px !important;
    overflow: hidden !important;
}

/* Reset text-indent for icons so they remain visible */
.wc_payment_method .icenox-pay-method-icon,
.wc_payment_method img {
    text-indent: 0 !important;
    max-width: 80px !important;
    max-height: 30px !important;
    object-fit: contain !important;
}

/* Override existing payment method list item styles - Grid compatible */
#payment .wc_payment_methods li,
#payment ul.payment_methods li,
.woocommerce-checkout #payment ul.payment_methods li {
    background: #FFFFFF !important;
    border: 1px solid #E5E7EA !important;
    border-radius: 6px !important;
    padding: 0px !important;
    margin: 0 !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    flex-wrap: nowrap !important;
    width: 100% !important;
    height: 43px !important;
    display: flex !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.2s ease !important;
    list-style: none !important;
}

/* Override hover effects */
#payment .wc_payment_methods li:hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Override label styling within payment methods */
#payment .wc_payment_methods li label {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0px !important;
    line-height: 0px !important;
    color: transparent !important;
    font-weight: normal !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0px !important;
    padding: 0px !important;
}

/* Ensure payment box is hidden */
#payment .wc_payment_methods li .payment_box {
    display: none !important;
    width: 0px !important;
    margin: 0px !important;
    padding: 0px !important;
}

.woocommerce-checkout-payment h3 {
padding-bottom: 10px;
    border-bottom: 1px solid #E5E7EA;
    margin-bottom: 29px!important;
}

.wc_payment_methods {
border:0px!important;
}
jQuery(document).ready(function($) {
    'use strict';

    // Handle quantity controls
    $('.quantity-btn').on('click', function(e) {
        e.preventDefault();
        
        var $button = $(this);
        var cartItemKey = $button.data('cart-item-key');
        var $quantityDisplay = $button.siblings('.quantity-display');
        var currentQuantity = parseInt($quantityDisplay.text());
        var newQuantity = currentQuantity;
        
        if ($button.hasClass('quantity-plus')) {
            newQuantity = currentQuantity + 1;
        } else if ($button.hasClass('quantity-minus') && currentQuantity > 1) {
            newQuantity = currentQuantity - 1;
        }
        
        if (newQuantity !== currentQuantity) {
            updateCartQuantity(cartItemKey, newQuantity, $quantityDisplay);
        }
    });

    function updateCartQuantity(cartItemKey, quantity, $quantityDisplay) {
        // Show loading state
        $quantityDisplay.text('...');
        
        // Prepare data for AJAX request
        var data = {
            action: 'update_cart_quantity',
            cart_item_key: cartItemKey,
            quantity: quantity,
            security: wc_checkout_params.update_order_review_nonce
        };

        // Send AJAX request
        $.ajax({
            type: 'POST',
            url: wc_checkout_params.ajax_url,
            data: data,
            success: function(response) {
                if (response.success) {
                    // Update quantity display
                    $quantityDisplay.text(quantity);
                    
                    // Trigger checkout update
                    $('body').trigger('update_checkout');
                } else {
                    // Revert quantity display on error
                    location.reload();
                }
            },
            error: function() {
                // Revert quantity display on error
                location.reload();
            }
        });
    }

    // Handle coupon application
    $('#apply-coupon-btn').on('click', function(e) {
        e.preventDefault();
        
        var $button = $(this);
        var $input = $('#coupon_code');
        var couponCode = $input.val().trim();
        
        if (!couponCode) {
            return;
        }
        
        // Show loading state
        $button.prop('disabled', true).text('Applying...');
        
        // Apply coupon
        var data = {
            action: 'woocommerce_apply_coupon',
            coupon_code: couponCode,
            security: wc_checkout_params.apply_coupon_nonce
        };

        $.ajax({
            type: 'POST',
            url: wc_checkout_params.wc_ajax_url.toString().replace('%%endpoint%%', 'apply_coupon'),
            data: data,
            success: function(response) {
                if (response.result === 'success') {
                    $input.val('');
                    $('body').trigger('update_checkout');
                } else {
                    // Show error message
                    if (response.messages) {
                        alert(response.messages);
                    }
                }
                
                // Reset button
                $button.prop('disabled', false).html('Apply <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 0L10.5 5.5L16 8L10.5 10.5L8 16L5.5 10.5L0 8L5.5 5.5L8 0Z" fill="currentColor"/></svg>');
            },
            error: function() {
                // Reset button
                $button.prop('disabled', false).html('Apply <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 0L10.5 5.5L16 8L10.5 10.5L8 16L5.5 10.5L0 8L5.5 5.5L8 0Z" fill="currentColor"/></svg>');
                alert('Error applying coupon. Please try again.');
            }
        });
    });

    // Allow Enter key to apply coupon
    $('#coupon_code').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $('#apply-coupon-btn').click();
        }
    });
});

jQuery(document).ready(function($) {
    'use strict';



    // Handle coupon application
    $('#apply-coupon-btn').on('click', function(e) {
        e.preventDefault();
        
        var $button = $(this);
        var $input = $('#coupon_code');
        var couponCode = $input.val().trim();
        
        if (!couponCode) {
            return;
        }
        
        // Show loading state
        $button.prop('disabled', true).text('Applying...');
        
        // Apply coupon
        var data = {
            action: 'woocommerce_apply_coupon',
            coupon_code: couponCode,
            security: wc_checkout_params.apply_coupon_nonce
        };

        $.ajax({
            type: 'POST',
            url: wc_checkout_params.wc_ajax_url.toString().replace('%%endpoint%%', 'apply_coupon'),
            data: data,
            success: function(response) {
                if (response.result === 'success') {
                    $input.val('');
                    $('body').trigger('update_checkout');
                } else {
                    // Show error message
                    if (response.messages) {
                        alert(response.messages);
                    }
                }
                
                // Reset button
                $button.prop('disabled', false).html('Apply <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 0L10.5 5.5L16 8L10.5 10.5L8 16L5.5 10.5L0 8L5.5 5.5L8 0Z" fill="currentColor"/></svg>');
            },
            error: function() {
                // Reset button
                $button.prop('disabled', false).html('Apply <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 0L10.5 5.5L16 8L10.5 10.5L8 16L5.5 10.5L0 8L5.5 5.5L8 0Z" fill="currentColor"/></svg>');
                alert('Error applying coupon. Please try again.');
            }
        });
    });

    // Allow Enter key to apply coupon
    $('#coupon_code').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $('#apply-coupon-btn').click();
        }
    });
});

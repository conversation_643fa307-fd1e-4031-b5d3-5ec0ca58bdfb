(function ($){

    $(document).ready(function () {

        $('body').on('change input', '.product-option-field.field-type-number input[type="number"]', function (){

            let val = $(this).val();
            let min = parseFloat($(this).attr('min'));
            let max = parseFloat($(this).attr('max'));

            if(val < min){
                $(this).val(min);
            }
            if (val > max){
                $(this).val(max);
            }

            checkConditions();
            updatePrice();

        });

        $('body').on('click', '.product-option-field.field-type-number .minus', function (){
            let input = $(this).parent().find('input');
            let val = parseFloat(input.val()) - 1;
            let min = parseFloat(input.attr('min'));

            if(val < min){
                input.val(min);
            }else(
                input.val(val)
            );
            input.trigger('change');
        });

        $('body').on('click', '.product-option-field.field-type-number .plus', function (){
            let input = $(this).parent().find('input');
            let val = parseFloat(input.val()) + 1;
            let max = parseFloat(input.attr('max'));

            if(val > max){
                input.val(max);
            }else(
                input.val(val)
            );
            input.trigger('change');
        });

        $('body').on('click', '#customer_details .checkout-steps .checkout-step', function (){
            $('body #customer_details .checkout-steps .checkout-step').removeClass('active');
            $(this).addClass('active');
            let step = $(this).attr('data-step');

            $('body .checkout-step-body').removeClass('active');
            $('body .checkout-step-body[data-step="'+step+'"]').addClass('active');

        });

        checkConditions();

        $('body').on('click', '.product-option-field select', function (){
            if($(this).hasClass('open')){
                $(this).removeClass('open');
            }else{
                $(this).addClass('open');
            }
        });

        $('body').on('focusout', '.product-option-field select', function (){
            $(this).removeClass('open');
        });

        $('body').on('change', '.product-option-field input', function () {
            if($(this).parents('li').hasClass('active')){
                $(this).parents('li').removeClass('active');
            }else{
                $(this).parents('.field-type-button').find('li.active').removeClass('active');
                $(this).parents('li').addClass('active');
            }
        });

        $('body').on('change', '.product-option-field input, .product-option-field select', function (event) {
            checkConditions();
            updatePrice();
            $('body').trigger('update_checkout');
        });

        $('body').on('click', '.checkout-step-body .next-step', function (e){
            e.preventDefault();
           $('body .checkout-steps .checkout-step.active').removeClass('active');
           $('body .checkout-steps .checkout-step[data-step="2"]').addClass('active');

           $('body .checkout-step-body.active').removeClass('active');
           $('body .checkout-step-body[data-step="2"]').addClass('active');
        });

        $('body').on('click', '.checkout-step-body .prev-step', function (e){
            e.preventDefault();
            $('body .checkout-steps .checkout-step.active').removeClass('active');
            $('body .checkout-steps .checkout-step[data-step="1"]').addClass('active');

            $('body .checkout-step-body.active').removeClass('active');
            $('body .checkout-step-body[data-step="1"]').addClass('active');
        });

        

    });

    function safeParseFloat(v){
    var x = parseFloat(v);
    return isNaN(x) ? 0 : x;
  }

  function updatePrice(){
    var cur    = $('#ccs-switcher').val() || ccs_params.base_currency;
    var rate   = safeParseFloat(ccs_params.rates[cur]);
    var symbol = ccs_params.symbols[cur] || '';

    var $inp  = $('input[name="product_new_price"]');
    var start = safeParseFloat($inp.data('start-price'));
    if (!start) {
      var txt = $('.criminalmodz-configurator .top-total .woocommerce-Price-amount').first().text();
      start = safeParseFloat(txt.replace(/[^0-9\.]/g,''));
    }
    var total = start * rate;

    $('[data-base-price]').each(function () {
        const basePrice = parseFloat($(this).attr('data-base-price')) || 0;
        const newPrice = (basePrice * rate).toFixed(2);
        $(this).attr('data-price', newPrice);
    });

    $('.product-option-field').not('.hidden-field-by-conditional').each(function(){
      var p = 0, $f = $(this);
      $f.find('input:checked').each(function(){
        p += safeParseFloat($(this).data('price'));
      });
      if ($f.find('input[type="text"]').length) {
        p += safeParseFloat($f.find('input[type="text"]').data('price'));
      }
      if ($f.find('input[type="number"]').length) {
        var $n = $f.find('input[type="number"]');
        p += safeParseFloat($n.val()) * safeParseFloat($n.data('price'));
      }
      if ($f.find('select').length) {
        p += safeParseFloat($f.find('option:selected').data('price'));
      }
      total += p * rate;
    });

    $inp.val(total.toFixed(2));
    $('.criminalmodz-configurator .top-total .woocommerce-Price-amount')
      .html(symbol + total.toFixed(2));
    $('#order_review tfoot .woocommerce-Price-amount')
      .html('<strong>' + symbol + total.toFixed(2) + '</strong>');
  }

  $(document).ready(function(){
    updatePrice();
  });

  // also recalc on any existing triggers:
  $('body').on('change input click', '.product-option-field input, .product-option-field select', function(){
    updatePrice();
  });

    function checkConditions() {

        if($('body .field-conditions').length > 0) {

            $('body .field-conditions').each(function () {
                let condition_field = $(this);
                let conditions = JSON.parse(condition_field.attr('data-conditions'));
                let show_field = true;

                if(conditions.length > 0) {
                    $.each(conditions, function(index, item) {

                        let target_input = $('#field-' + item.field).find('input[value="' + item.value + '"]');

                        if(target_input.length == 0) {
                            target_input = $('#field-' + item.field).find('select[name="' + item.field + '"]');
                        }

                        if (target_input.length == 0) {
                            show_field = false;
                            return;
                        }

                        if(item.operator == 'equal'){

                            if(target_input.is(':checkbox') || target_input.is(':radio')) {
                                if(!target_input.is(':checked')) {
                                    show_field = false;
                                    return;
                                }
                            }else{
                                if(target_input.val() != item.value) {
                                    show_field = false;
                                    return;
                                }
                            }
                        }else{

                            if(target_input.is(':checkbox') || target_input.is(':radio')) {
                                if(target_input.is(':checked')) {
                                    show_field = false;
                                    return;
                                }
                            }else{
                                if(target_input.val() == item.value) {
                                    show_field = false;
                                    return;
                                }
                            }

                        }
                    });
                }

                if(show_field) {
                    condition_field.parents('.product-option-field').removeClass('hidden-field-by-conditional');
                }else{
                    condition_field.parents('.product-option-field').addClass('hidden-field-by-conditional');
                }

            });

        }

        refreshValidateInput();
        refreshAddMetaInput();

    }

    function refreshAddMetaInput(){
        $('body input[name="fields_to_add_to_meta"]').val('');
        let fields_to_add_to_meta = [];

        $('body .product-option-field').each(function () {

            if(!$(this).hasClass('hidden-field-by-conditional')){
                fields_to_add_to_meta.push($(this).attr('data-field-slug'));
            }

            $('body input[name="fields_to_add_to_meta"]').val(JSON.stringify(fields_to_add_to_meta));

        });
    }

    function refreshValidateInput(){
        $('body input[name="fields_to_validate"]').val('');
        let fields_to_validate = [];

        $('body .product-option-field').each(function () {

            if(!$(this).hasClass('hidden-field-by-conditional') && $(this).hasClass('required-field')){
                fields_to_validate.push($(this).attr('data-field-slug'));
            }

            $('body input[name="fields_to_validate"]').val(JSON.stringify(fields_to_validate));

        });
    }

    // Dark mode toggle functionality
    $(document).on('click', '.dark-mode-toggle', function() {
        $('body').toggleClass('dark-mode');
        
        // Save preference to localStorage
        if ($('body').hasClass('dark-mode')) {
            localStorage.setItem('checkout-dark-mode', 'enabled');
        } else {
            localStorage.removeItem('checkout-dark-mode');
        }
    });

    // Check for saved dark mode preference on page load
    if (localStorage.getItem('checkout-dark-mode') === 'enabled') {
        $('body').addClass('dark-mode');
    }

    // Move payment methods to separate section (fallback)
    function movePaymentMethods() {
        var $paymentSection = $('#payment.woocommerce-checkout-payment');
        var $targetContainer = $('.payment_section');

        if ($paymentSection.length && $targetContainer.length && !$targetContainer.find('#payment').length) {
            $paymentSection.detach().appendTo($targetContainer);
        }

        // Always ensure only one place order button exists (in order summary)
        removeDuplicatePlaceOrderButtons();
    }

    // Function to remove duplicate place order buttons
    function removeDuplicatePlaceOrderButtons() {
        // Remove any place order buttons from payment section
        $('.payment_section').find('#place_order, .place-order, button[name="woocommerce_checkout_place_order"]').remove();

        // Remove any place order buttons that might be outside the order review
        $('body').find('#place_order, button[name="woocommerce_checkout_place_order"]').not('#order_review #place_order, #order_review button[name="woocommerce_checkout_place_order"]').remove();

        // Ensure we have exactly one place order button in the order review
        var $orderReviewButtons = $('#order_review').find('#place_order, button[name="woocommerce_checkout_place_order"]');
        if ($orderReviewButtons.length > 1) {
            $orderReviewButtons.not(':first').remove();
        }

        // Also remove duplicate privacy policy text
        removeDuplicatePrivacyPolicyText();
    }

    // Function to remove duplicate privacy policy text
    function removeDuplicatePrivacyPolicyText() {
        // Remove privacy policy text from payment section
        $('.payment_section').find('.woocommerce-terms-and-conditions-wrapper, .woocommerce-privacy-policy-text').remove();

        // Ensure only one privacy policy text exists (keep the one in order review)
        var $privacyTexts = $('body').find('.woocommerce-terms-and-conditions-wrapper');
        if ($privacyTexts.length > 1) {
            // Keep only the one in order review, remove others
            $privacyTexts.not('#order_review .woocommerce-terms-and-conditions-wrapper').remove();
        }
    }
    
    // Move on initial load
    movePaymentMethods();

    // Re-move after AJAX updates
    $(document.body).on('updated_checkout', function() {
        setTimeout(function() {
            movePaymentMethods();
            // Additional cleanup after a longer delay to catch any late-loading buttons
            setTimeout(removeDuplicatePlaceOrderButtons, 200);
        }, 100);
    });

    // Also run cleanup on document ready and after any DOM changes
    $(document).ready(function() {
        setTimeout(removeDuplicatePlaceOrderButtons, 500);
    });

    // Watch for any new place order buttons being added and remove duplicates
    if (window.MutationObserver) {
        var observer = new MutationObserver(function(mutations) {
            var shouldCheck = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (var i = 0; i < mutation.addedNodes.length; i++) {
                        var node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // Element node
                            if (node.id === 'place_order' ||
                                (node.tagName === 'BUTTON' && node.name === 'woocommerce_checkout_place_order') ||
                                (node.className && (node.className.includes('woocommerce-terms-and-conditions-wrapper') || node.className.includes('woocommerce-privacy-policy-text'))) ||
                                (node.querySelector && node.querySelector('#place_order, button[name="woocommerce_checkout_place_order"], .woocommerce-terms-and-conditions-wrapper, .woocommerce-privacy-policy-text'))) {
                                shouldCheck = true;
                                break;
                            }
                        }
                    }
                }
            });
            if (shouldCheck) {
                setTimeout(removeDuplicatePlaceOrderButtons, 50);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Coupon application functionality
    $(document.body).on('click', '#apply-coupon-btn', function(e) {
        e.preventDefault();
        
        var couponCode = $('#coupon_code').val();
        var $button = $(this);
        
        if (!couponCode.trim()) {
            alert('Please enter a coupon code.');
            return;
        }
        
        // Disable button and show loading
        $button.prop('disabled', true).html('Applying...');
        
        // Apply coupon via AJAX
        $.ajax({
            type: 'POST',
            url: wc_checkout_params.ajax_url,
            data: {
                action: 'woocommerce_apply_coupon',
                coupon_code: couponCode,
                security: wc_checkout_params.apply_coupon_nonce
            },
            success: function(response) {
                if (response.success) {
                    // Trigger checkout update to refresh totals
                    $(document.body).trigger('update_checkout');
                    $('#coupon_code').val(''); // Clear input
                } else {
                    alert(response.data || 'Failed to apply coupon');
                }
            },
            error: function() {
                alert('Failed to apply coupon. Please try again.');
            },
            complete: function() {
                // Re-enable button
                $button.prop('disabled', false).html('Apply <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 0L10.5 5.5L16 8L10.5 10.5L8 16L5.5 10.5L0 8L5.5 5.5L8 0Z" fill="currentColor"/></svg>');
            }
        });
    });

    // Allow Enter key to apply coupon
    $(document.body).on('keypress', '#coupon_code', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $('#apply-coupon-btn').click();
        }
    });

})(jQuery);
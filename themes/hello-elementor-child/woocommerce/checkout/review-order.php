<?php

defined( 'ABSPATH' ) || exit;
?>

<div class="checkout-order-review">
    <?php
    do_action( 'woocommerce_review_order_before_cart_contents' );

    foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
        $_product = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );

        if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_checkout_cart_item_visible', true, $cart_item, $cart_item_key ) ) {
            $description = $_product->get_description();
            $product_image = $_product->get_image( 'thumbnail' );
            ?>
            <div class="product-card <?php echo esc_attr( apply_filters( 'woocommerce_cart_item_class', 'cart_item', $cart_item, $cart_item_key ) ); ?>">
                <div class="product-image">
                    <?php echo $product_image; ?>
                </div>
                <div class="product-details">
                    <div class="product-title">
                        <?php echo wp_kses_post( apply_filters( 'woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key ) ); ?>
                    </div>
                    <?php if ( !empty( $description ) ) { ?>
                        <div class="product-description">
                            <?php echo wp_kses_post( $description ); ?>
                        </div>
                    <?php } ?>
                    <div class="product-price">
                        <?php echo apply_filters( 'woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] ), $cart_item, $cart_item_key ); ?>
                    </div>
                </div>

            </div>
            <?php
        }
    }

    do_action( 'woocommerce_review_order_after_cart_contents' );
    ?>

    <?php if ( wc_coupons_enabled() ) : ?>
    <div class="coupon-section">
        <div class="coupon-form-container">
            <input type="text" name="coupon_code" class="coupon-input" placeholder="<?php esc_attr_e( 'Use Coupon Code', 'woocommerce' ); ?>" id="coupon_code" value="" />
            <button type="button" class="coupon-apply-btn" id="apply-coupon-btn">
                <?php esc_html_e( 'Apply', 'woocommerce' ); ?>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 0L10.5 5.5L16 8L10.5 10.5L8 16L5.5 10.5L0 8L5.5 5.5L8 0Z" fill="currentColor"/>
                </svg>
            </button>
        </div>
    </div>
    <?php endif; ?>

    <div class="order-totals">
        <?php foreach ( WC()->cart->get_coupons() as $code => $coupon ) : ?>
            <div class="total-row coupon-row coupon-<?php echo esc_attr( sanitize_title( $code ) ); ?>">
                <span class="total-label"><?php wc_cart_totals_coupon_label( $coupon ); ?></span>
                <span class="total-value"><?php wc_cart_totals_coupon_html( $coupon ); ?></span>
            </div>
        <?php endforeach; ?>

        <div class="total-row subtotal-row">
            <span class="total-label"><?php esc_html_e( 'Subtotal', 'woocommerce' ); ?></span>
            <span class="total-value"><?php wc_cart_totals_subtotal_html(); ?></span>
        </div>

        <?php if ( WC()->cart->needs_shipping() && WC()->cart->show_shipping() ) : ?>
            <?php do_action( 'woocommerce_review_order_before_shipping' ); ?>
            <?php
            // Custom shipping display for new layout
            $packages = WC()->shipping()->get_packages();
            foreach ( $packages as $i => $package ) {
                $chosen_method = isset( WC()->session->chosen_shipping_methods[ $i ] ) ? WC()->session->chosen_shipping_methods[ $i ] : '';
                $product_names = array();

                if ( count( $packages ) > 1 ) {
                    foreach ( $package['contents'] as $item_id => $values ) {
                        $product_names[ $item_id ] = $values['data']->get_name() . ' &times;' . $values['quantity'];
                    }
                    $product_names = apply_filters( 'woocommerce_shipping_package_details_array', $product_names, $package );
                }

                if ( count( $package['rates'] ) > 0 ) {
                    foreach ( $package['rates'] as $method ) {
                        if ( $method->id === $chosen_method ) {
                            ?>
                            <div class="total-row shipping-row">
                                <span class="total-label"><?php echo esc_html( $method->get_label() ); ?></span>
                                <span class="total-value"><?php echo wc_cart_totals_shipping_method_label( $method ); ?></span>
                            </div>
                            <?php
                            break;
                        }
                    }
                }
            }
            ?>
            <?php do_action( 'woocommerce_review_order_after_shipping' ); ?>
        <?php endif; ?>

        <?php foreach ( WC()->cart->get_fees() as $fee ) : ?>
            <div class="total-row fee-row">
                <span class="total-label"><?php echo esc_html( $fee->name ); ?></span>
                <span class="total-value"><?php wc_cart_totals_fee_html( $fee ); ?></span>
            </div>
        <?php endforeach; ?>

        <?php if ( wc_tax_enabled() && ! WC()->cart->display_prices_including_tax() ) : ?>
            <?php if ( 'itemized' === get_option( 'woocommerce_tax_total_display' ) ) : ?>
                <?php foreach ( WC()->cart->get_tax_totals() as $code => $tax ) : ?>
                    <div class="total-row tax-row tax-rate-<?php echo esc_attr( sanitize_title( $code ) ); ?>">
                        <span class="total-label"><?php echo esc_html( $tax->label ); ?></span>
                        <span class="total-value"><?php echo wp_kses_post( $tax->formatted_amount ); ?></span>
                    </div>
                <?php endforeach; ?>
            <?php else : ?>
                <div class="total-row tax-row">
                    <span class="total-label"><?php echo esc_html( WC()->countries->tax_or_vat() ); ?></span>
                    <span class="total-value"><?php wc_cart_totals_taxes_total_html(); ?></span>
                </div>
            <?php endif; ?>
        <?php endif; ?>

        <?php do_action( 'woocommerce_review_order_before_order_total' ); ?>

        <div class="total-row final-total-row">
            <span class="total-label"><?php esc_html_e( 'Total', 'woocommerce' ); ?></span>
            <span class="total-currency">USD</span>
            <span class="total-value"><?php wc_cart_totals_order_total_html(); ?></span>
        </div>

        <?php do_action( 'woocommerce_review_order_after_order_total' ); ?>
    </div>
    <div class="pay-now-section">
        <noscript>
            <?php
            /* translators: $1 and $2 opening and closing emphasis tags respectively */
            printf( esc_html__( 'Since your browser does not support JavaScript, or it is disabled, please ensure you click the %1$sUpdate Totals%2$s button before placing your order. You may be charged more than the amount stated above if you fail to do so.', 'woocommerce' ), '<em>', '</em>' );
            ?>
            <br/><button type="submit" class="button alt<?php echo esc_attr( wc_wp_theme_get_element_class_name( 'button' ) ? ' ' . wc_wp_theme_get_element_class_name( 'button' ) : '' ); ?>" name="woocommerce_checkout_update_totals" value="<?php esc_attr_e( 'Update totals', 'woocommerce' ); ?>"><?php esc_html_e( 'Update totals', 'woocommerce' ); ?></button>
        </noscript>

        <?php do_action( 'woocommerce_review_order_before_submit' ); ?>

        <?php $order_button_text = __( 'Pay Now', 'woocommerce' ); ?>
        <?php echo apply_filters( 'woocommerce_order_button_html', '<button type="submit" class="pay-now-btn button alt' . esc_attr( wc_wp_theme_get_element_class_name( 'button' ) ? ' ' . wc_wp_theme_get_element_class_name( 'button' ) : '' ) . '" name="woocommerce_checkout_place_order" id="place_order" value="' . esc_attr( $order_button_text ) . '" data-value="' . esc_attr( $order_button_text ) . '">' . esc_html( $order_button_text ) . ' <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 12L10 8L6 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg></button>' ); ?>

        <?php do_action( 'woocommerce_review_order_after_submit' ); ?>

        <?php wp_nonce_field( 'woocommerce-process_checkout', 'woocommerce-process-checkout-nonce' ); ?>
    </div>
</div>

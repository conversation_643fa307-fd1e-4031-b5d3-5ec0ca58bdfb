<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// Add custom checkout header
?>
<div class="checkout-top-bar">
    <div class="checkout-top-bar-inner">
        <div class="top-bar-left">
            <a href="<?php echo home_url(); ?>" class="back-arrow">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_154_506)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M3.41679 6.99996H14.9998C15.265 6.99996 15.5194 7.10532 15.7069 7.29285C15.8944 7.48039 15.9998 7.73474 15.9998 7.99996C15.9998 8.26518 15.8944 8.51953 15.7069 8.70707C15.5194 8.8946 15.265 8.99996 14.9998 8.99996H3.41679L8.00779 13.591C8.19556 13.7786 8.3011 14.0331 8.3012 14.2986C8.30129 14.5641 8.19593 14.8187 8.00829 15.0065C7.82065 15.1942 7.5661 15.2998 7.30064 15.2999C7.03518 15.3 6.78056 15.1946 6.59279 15.007L0.292786 8.70696C0.105315 8.51943 0 8.26512 0 7.99996C0 7.73479 0.105315 7.48049 0.292786 7.29296L6.59279 0.992959C6.78069 0.805318 7.03544 0.700008 7.30099 0.700196C7.56655 0.700383 7.82115 0.806053 8.00879 0.993959C8.19643 1.18186 8.30174 1.43661 8.30155 1.70217C8.30136 1.96772 8.19569 2.22232 8.00779 2.40996L3.41679 6.99996Z" fill="#8792A2"/>
                    </g>
                    <defs>
                        <clipPath id="clip0_154_506">
                            <rect width="16" height="16" fill="white"/>
                        </clipPath>
                    </defs>
                </svg>
            </a>
            <div class="checkout-logo">
                <img src="<?php echo get_stylesheet_directory_uri() . '/assets/img/logo.svg'; ?>" alt="<?php echo get_bloginfo( 'name' ); ?>" width="164">
            </div>
        </div>
        <div class="top-bar-right">
            <span class="dark-mode-text">Dark Mode</span>
            <div class="dark-mode-toggle">
                <svg width="70" height="42" viewBox="0 0 70 42" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#filter0_ddd_154_510)">
                        <rect x="5" y="3" width="60" height="32" rx="16" fill="url(#paint0_linear_154_510)"/>
                    </g>
                    <g filter="url(#filter1_dd_154_510)">
                        <circle cx="22" cy="19" r="12" fill="white"/>
                    </g>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M47.1652 13.1299C47.3408 13.2754 47.4181 13.514 47.3629 13.74C47.2562 14.1766 47.1994 14.6345 47.1994 15.1069C47.1994 18.1691 49.5923 20.6515 52.544 20.6515C53.4171 20.6515 54.2398 20.4348 54.9663 20.0508C55.1665 19.945 55.4079 19.9705 55.5836 20.116C55.7592 20.2615 55.8365 20.5001 55.7813 20.7261C55.0689 23.642 52.5231 25.8 49.4899 25.8C45.9056 25.8 43 22.7857 43 19.0673C43 16.4467 44.4434 14.1768 46.5479 13.0647C46.7481 12.9589 46.9895 12.9844 47.1652 13.1299Z" fill="white"/>
                    <g filter="url(#filter2_ddd_154_510)">
                        <path d="M22.4 13C22.7314 13 23 13.2686 23 13.6V14.8C23 15.1314 22.7314 15.4 22.4 15.4C22.0686 15.4 21.8 15.1314 21.8 14.8V13.6C21.8 13.2686 22.0686 13 22.4 13Z" fill="url(#paint1_linear_154_510)"/>
                        <path d="M22.4 23.4C22.7314 23.4 23 23.6686 23 24V25.2C23 25.5314 22.7314 25.8 22.4 25.8C22.0686 25.8 21.8 25.5314 21.8 25.2V24C21.8 23.6686 22.0686 23.4 22.4 23.4Z" fill="url(#paint2_linear_154_510)"/>
                        <path d="M22.4 17C21.0745 17 20 18.0745 20 19.4C20 20.7255 21.0745 21.8 22.4 21.8C23.7255 21.8 24.8 20.7255 24.8 19.4C24.8 18.0745 23.7255 17 22.4 17Z" fill="url(#paint3_linear_154_510)"/>
                        <path d="M26.9254 15.7231C27.1597 15.4888 27.1597 15.1089 26.9254 14.8746C26.6911 14.6402 26.3112 14.6402 26.0769 14.8746L25.2284 15.7231C24.9941 15.9574 24.9941 16.3373 25.2284 16.5716C25.4627 16.8059 25.8426 16.8059 26.0769 16.5716L26.9254 15.7231Z" fill="url(#paint4_linear_154_510)"/>
                        <path d="M19.5715 23.077C19.8058 22.8427 19.8058 22.4628 19.5715 22.2285C19.3372 21.9942 18.9573 21.9942 18.723 22.2285L17.8745 23.077C17.6402 23.3113 17.6402 23.6912 17.8745 23.9255C18.1088 24.1598 18.4887 24.1598 18.723 23.9255L19.5715 23.077Z" fill="url(#paint5_linear_154_510)"/>
                        <path d="M28.8 19.4C28.8 19.7314 28.5314 20 28.2 20H27C26.6686 20 26.4 19.7314 26.4 19.4C26.4 19.0686 26.6686 18.8 27 18.8H28.2C28.5314 18.8 28.8 19.0686 28.8 19.4Z" fill="url(#paint6_linear_154_510)"/>
                        <path d="M18.4 19.4C18.4 19.7314 18.1314 20 17.8 20H16.6C16.2686 20 16 19.7314 16 19.4C16 19.0686 16.2686 18.8 16.6 18.8H17.8C18.1314 18.8 18.4 19.0686 18.4 19.4Z" fill="url(#paint7_linear_154_510)"/>
                        <path d="M26.0768 23.9254C26.3111 24.1598 26.691 24.1598 26.9253 23.9254C27.1597 23.6911 27.1597 23.3112 26.9253 23.0769L26.0768 22.2284C25.8425 21.9941 25.4626 21.9941 25.2283 22.2284C24.994 22.4627 24.994 22.8426 25.2283 23.0769L26.0768 23.9254Z" fill="url(#paint8_linear_154_510)"/>
                        <path d="M18.7229 16.5715C18.9572 16.8058 19.3371 16.8058 19.5714 16.5715C19.8058 16.3372 19.8057 15.9573 19.5714 15.723L18.7229 14.8745C18.4886 14.6402 18.1087 14.6402 17.8744 14.8745C17.6401 15.1088 17.6401 15.4887 17.8744 15.723L18.7229 16.5715Z" fill="url(#paint9_linear_154_510)"/>
                    </g>
                    <defs>
                        <filter id="filter0_ddd_154_510" x="0" y="0" width="70" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="1"/>
                            <feGaussianBlur stdDeviation="0.5"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_154_510"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect2_dropShadow_154_510"/>
                            <feOffset/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.16 0"/>
                            <feBlend mode="normal" in2="effect1_dropShadow_154_510" result="effect2_dropShadow_154_510"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="2"/>
                            <feGaussianBlur stdDeviation="2.5"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.08 0"/>
                            <feBlend mode="normal" in2="effect2_dropShadow_154_510" result="effect3_dropShadow_154_510"/>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_154_510" result="shape"/>
                        </filter>
                        <filter id="filter1_dd_154_510" x="4" y="4" width="36" height="36" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="3"/>
                            <feGaussianBlur stdDeviation="3"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.08 0"/>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_154_510"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="1"/>
                            <feGaussianBlur stdDeviation="0.5"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
                            <feBlend mode="normal" in2="effect1_dropShadow_154_510" result="effect2_dropShadow_154_510"/>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_154_510" result="shape"/>
                        </filter>
                        <filter id="filter2_ddd_154_510" x="11" y="10" width="22.8008" height="22.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="1"/>
                            <feGaussianBlur stdDeviation="0.5"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_154_510"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect2_dropShadow_154_510"/>
                            <feOffset/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.16 0"/>
                            <feBlend mode="normal" in2="effect1_dropShadow_154_510" result="effect2_dropShadow_154_510"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="2"/>
                            <feGaussianBlur stdDeviation="2.5"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.08 0"/>
                            <feBlend mode="normal" in2="effect3_dropShadow_154_510" result="effect3_dropShadow_154_510"/>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_154_510" result="shape"/>
                        </filter>
                        <linearGradient id="paint0_linear_154_510" x1="5" y1="19" x2="65" y2="19" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"/>
                            <stop offset="1" stop-color="#FF4747"/>
                        </linearGradient>
                        <linearGradient id="paint1_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"/>
                            <stop offset="1" stop-color="#FF4747"/>
                        </linearGradient>
                        <linearGradient id="paint2_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"/>
                            <stop offset="1" stop-color="#FF4747"/>
                        </linearGradient>
                        <linearGradient id="paint3_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"/>
                            <stop offset="1" stop-color="#FF4747"/>
                        </linearGradient>
                        <linearGradient id="paint4_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"/>
                            <stop offset="1" stop-color="#FF4747"/>
                        </linearGradient>
                        <linearGradient id="paint5_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"/>
                            <stop offset="1" stop-color="#FF4747"/>
                        </linearGradient>
                        <linearGradient id="paint6_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"/>
                            <stop offset="1" stop-color="#FF4747"/>
                        </linearGradient>
                        <linearGradient id="paint7_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"/>
                            <stop offset="1" stop-color="#FF4747"/>
                        </linearGradient>
                        <linearGradient id="paint8_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"/>
                            <stop offset="1" stop-color="#FF4747"/>
                        </linearGradient>
                        <linearGradient id="paint9_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"/>
                            <stop offset="1" stop-color="#FF4747"/>
                        </linearGradient>
                    </defs>
                </svg>
            </div>
        </div>
    </div>
</div>
<?php

do_action( 'woocommerce_before_checkout_form', $checkout );


// If checkout registration is disabled and not logged in, the user cannot checkout.
if ( ! $checkout->is_registration_enabled() && $checkout->is_registration_required() && ! is_user_logged_in() ) {
	echo esc_html( apply_filters( 'woocommerce_checkout_must_be_logged_in_message', __( 'You must be logged in to checkout.', 'woocommerce' ) ) );
	return;
}
$with_product_options = false;
$product_options = '';
if (!empty(WC()->cart->get_cart())) {

    $cart_items = WC()->cart->get_cart();
    $first_item = reset($cart_items);
    $product_id = $first_item['product_id'];

    if(get_field('show_product_options', $product_id)){
        $product_options = get_field('product_options', $product_id);

        if (!empty($product_options) && is_array($product_options)) {
            $with_product_options = true;
        }
    }

}


?>

<form name="checkout" method="post"
      class="checkout woocommerce-checkout"
      data-symbol="<?php echo get_woocommerce_currency_symbol(); ?>"
      action="<?php echo esc_url( wc_get_checkout_url() ); ?>" enctype="multipart/form-data">

	<?php if ( $checkout->get_checkout_fields() ) : ?>

		<?php do_action( 'woocommerce_checkout_before_customer_details' ); ?>

		      <div class="checkout-main-container">
		          <div class="checkout-column-1">
		              <div id="customer_details">

		                  <?php if($with_product_options){ ?>
		                      <div class="checkout-steps">
		                          <div class="checkout-step active" data-step="1">
		                              <span>01</span>
		                              <span><?php echo esc_html__('Customization', 'woocommerce'); ?></span>
		                          </div>
		                          <div class="checkout-step" data-step="2">
		                              <span>02</span>
		                              <span><?php echo esc_html__('Checkout', 'woocommerce'); ?></span>
		                          </div>
		                      </div>
		                  <?php } ?>

		                  <div class="billing_section">

		                      <?php if($with_product_options){
		                          $product_price = get_post_meta($product_id, '_price', true);
		                          echo '<div class="checkout-step-body active" data-step="1">';
		                          foreach ($product_options as $product_option) {
		                              do_action('render_option', $product_option['product_option'], $product_id);
		                          }
		                          echo '<input type="hidden" name="product_new_price" data-start-price="'.$product_price.'" value="'.$product_price.'">';
		                          echo '<input type="hidden" name="fields_to_validate" value="">';
		                          echo '<input type="hidden" name="fields_to_add_to_meta" value="">';
		                          echo '<div class="next-step"><button class="button">'.__('Next Step', 'nergetix').'</button></div>';
		                          echo '</div>';
		                      } ?>

		                      <?php
		                      if($with_product_options){
		                          echo '<div class="checkout-step-body" data-step="2">';
		                      }
		                      do_action( 'woocommerce_checkout_billing' );
		                      if ($with_product_options) {
		                          echo '</div>';
		                      }
		                      ?>
		                  </div>

		                  <div class="shipping_section">
		                      <?php do_action( 'woocommerce_checkout_shipping' ); ?>
		                  </div>

		              </div>

		              <?php do_action( 'woocommerce_checkout_after_customer_details' ); ?>

		              <!-- Separate Payment Methods Section -->
		              <div class="payment_section">
		                  <?php do_action( 'woocommerce_checkout_payment_section' ); ?>
		              </div>
		          </div>

		          <div class="checkout-column-2">
		              <div id="order_review" class="woocommerce-checkout-review-order">

		                  <?php do_action( 'woocommerce_checkout_before_order_review_heading' ); ?>

		                  <h3 id="order_review_heading">
		                      <span class="product-image">
		                          <?php foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
		                              echo $cart_item['data']->get_image();
		                          } ?>
		                      </span>
		                  </h3>

		                  <?php do_action( 'woocommerce_checkout_before_order_review' ); ?>

		                  <?php do_action( 'woocommerce_checkout_order_review' ); ?>
		              </div>
		          </div>
		      </div>

	<?php endif; ?>

    <?php do_action( 'woocommerce_checkout_after_order_review' ); ?>

</form>

<?php do_action( 'woocommerce_after_checkout_form', $checkout ); ?>

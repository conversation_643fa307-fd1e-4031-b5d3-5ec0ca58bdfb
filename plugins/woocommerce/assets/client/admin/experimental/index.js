/*! For license information please see index.js.LICENSE.txt */
(()=>{var e={17697:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)){if(n.length){var a=o.apply(null,n);a&&e.push(a)}}else if("object"===i){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var s in n)r.call(n,s)&&n[s]&&e.push(s)}}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},13240:function(e){e.exports=function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,n){return t=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},t(e,n)}function n(e,r,o){return n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct:function(e,n,r){var o=[null];o.push.apply(o,n);var i=new(Function.bind.apply(e,o));return r&&t(i,r.prototype),i},n.apply(null,arguments)}function r(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var i=Object.hasOwnProperty,a=Object.setPrototypeOf,s=Object.isFrozen,l=Object.getPrototypeOf,c=Object.getOwnPropertyDescriptor,m=Object.freeze,u=Object.seal,p=Object.create,d="undefined"!=typeof Reflect&&Reflect,f=d.apply,h=d.construct;f||(f=function(e,t,n){return e.apply(t,n)}),m||(m=function(e){return e}),u||(u=function(e){return e}),h||(h=function(e,t){return n(e,r(t))});var g,v=C(Array.prototype.forEach),E=C(Array.prototype.pop),y=C(Array.prototype.push),x=C(String.prototype.toLowerCase),b=C(String.prototype.toString),w=C(String.prototype.match),_=C(String.prototype.replace),N=C(String.prototype.indexOf),k=C(String.prototype.trim),S=C(RegExp.prototype.test),T=(g=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return h(g,t)});function C(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return f(e,t,r)}}function O(e,t,n){var r;n=null!==(r=n)&&void 0!==r?r:x,a&&a(e,null);for(var o=t.length;o--;){var i=t[o];if("string"==typeof i){var l=n(i);l!==i&&(s(t)||(t[o]=l),i=l)}e[i]=!0}return e}function A(e){var t,n=p(null);for(t in e)!0===f(i,e,[t])&&(n[t]=e[t]);return n}function L(e,t){for(;null!==e;){var n=c(e,t);if(n){if(n.get)return C(n.get);if("function"==typeof n.value)return C(n.value)}e=l(e)}return function(e){return console.warn("fallback value for",e),null}}var R=m(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),D=m(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),M=m(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),I=m(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),P=m(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),j=m(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),F=m(["#text"]),H=m(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),B=m(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),U=m(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),z=m(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),G=u(/\{\{[\w\W]*|[\w\W]*\}\}/gm),V=u(/<%[\w\W]*|[\w\W]*%>/gm),W=u(/\${[\w\W]*}/gm),$=u(/^data-[\-\w.\u00B7-\uFFFF]/),q=u(/^aria-[\-\w]+$/),Y=u(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),K=u(/^(?:\w+script|data):/i),X=u(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Z=u(/^html$/i),J=u(/^[a-z][.\w]*(-[.\w]+)+$/i),Q=function(){return"undefined"==typeof window?null:window};return function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Q(),o=function(e){return t(e)};if(o.version="2.5.7",o.removed=[],!n||!n.document||9!==n.document.nodeType)return o.isSupported=!1,o;var i=n.document,a=n.document,s=n.DocumentFragment,l=n.HTMLTemplateElement,c=n.Node,u=n.Element,p=n.NodeFilter,d=n.NamedNodeMap,f=void 0===d?n.NamedNodeMap||n.MozNamedAttrMap:d,h=n.HTMLFormElement,g=n.DOMParser,C=n.trustedTypes,ee=u.prototype,te=L(ee,"cloneNode"),ne=L(ee,"nextSibling"),re=L(ee,"childNodes"),oe=L(ee,"parentNode");if("function"==typeof l){var ie=a.createElement("template");ie.content&&ie.content.ownerDocument&&(a=ie.content.ownerDocument)}var ae=function(t,n){if("object"!==e(t)||"function"!=typeof t.createPolicy)return null;var r=null,o="data-tt-policy-suffix";n.currentScript&&n.currentScript.hasAttribute(o)&&(r=n.currentScript.getAttribute(o));var i="dompurify"+(r?"#"+r:"");try{return t.createPolicy(i,{createHTML:function(e){return e},createScriptURL:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+i+" could not be created."),null}}(C,i),se=ae?ae.createHTML(""):"",le=a,ce=le.implementation,me=le.createNodeIterator,ue=le.createDocumentFragment,pe=le.getElementsByTagName,de=i.importNode,fe={};try{fe=A(a).documentMode?a.documentMode:{}}catch(e){}var he={};o.isSupported="function"==typeof oe&&ce&&void 0!==ce.createHTMLDocument&&9!==fe;var ge,ve,Ee=G,ye=V,xe=W,be=$,we=q,_e=K,Ne=X,ke=J,Se=Y,Te=null,Ce=O({},[].concat(r(R),r(D),r(M),r(P),r(F))),Oe=null,Ae=O({},[].concat(r(H),r(B),r(U),r(z))),Le=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Re=null,De=null,Me=!0,Ie=!0,Pe=!1,je=!0,Fe=!1,He=!0,Be=!1,Ue=!1,ze=!1,Ge=!1,Ve=!1,We=!1,$e=!0,qe=!1,Ye=!0,Ke=!1,Xe={},Ze=null,Je=O({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Qe=null,et=O({},["audio","video","img","source","image","track"]),tt=null,nt=O({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),rt="http://www.w3.org/1998/Math/MathML",ot="http://www.w3.org/2000/svg",it="http://www.w3.org/1999/xhtml",at=it,st=!1,lt=null,ct=O({},[rt,ot,it],b),mt=["application/xhtml+xml","text/html"],ut=null,pt=a.createElement("form"),dt=function(e){return e instanceof RegExp||e instanceof Function},ft=function(t){ut&&ut===t||(t&&"object"===e(t)||(t={}),t=A(t),ge=ge=-1===mt.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE,ve="application/xhtml+xml"===ge?b:x,Te="ALLOWED_TAGS"in t?O({},t.ALLOWED_TAGS,ve):Ce,Oe="ALLOWED_ATTR"in t?O({},t.ALLOWED_ATTR,ve):Ae,lt="ALLOWED_NAMESPACES"in t?O({},t.ALLOWED_NAMESPACES,b):ct,tt="ADD_URI_SAFE_ATTR"in t?O(A(nt),t.ADD_URI_SAFE_ATTR,ve):nt,Qe="ADD_DATA_URI_TAGS"in t?O(A(et),t.ADD_DATA_URI_TAGS,ve):et,Ze="FORBID_CONTENTS"in t?O({},t.FORBID_CONTENTS,ve):Je,Re="FORBID_TAGS"in t?O({},t.FORBID_TAGS,ve):{},De="FORBID_ATTR"in t?O({},t.FORBID_ATTR,ve):{},Xe="USE_PROFILES"in t&&t.USE_PROFILES,Me=!1!==t.ALLOW_ARIA_ATTR,Ie=!1!==t.ALLOW_DATA_ATTR,Pe=t.ALLOW_UNKNOWN_PROTOCOLS||!1,je=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,Fe=t.SAFE_FOR_TEMPLATES||!1,He=!1!==t.SAFE_FOR_XML,Be=t.WHOLE_DOCUMENT||!1,Ge=t.RETURN_DOM||!1,Ve=t.RETURN_DOM_FRAGMENT||!1,We=t.RETURN_TRUSTED_TYPE||!1,ze=t.FORCE_BODY||!1,$e=!1!==t.SANITIZE_DOM,qe=t.SANITIZE_NAMED_PROPS||!1,Ye=!1!==t.KEEP_CONTENT,Ke=t.IN_PLACE||!1,Se=t.ALLOWED_URI_REGEXP||Se,at=t.NAMESPACE||it,Le=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&dt(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Le.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&dt(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Le.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Le.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Fe&&(Ie=!1),Ve&&(Ge=!0),Xe&&(Te=O({},r(F)),Oe=[],!0===Xe.html&&(O(Te,R),O(Oe,H)),!0===Xe.svg&&(O(Te,D),O(Oe,B),O(Oe,z)),!0===Xe.svgFilters&&(O(Te,M),O(Oe,B),O(Oe,z)),!0===Xe.mathMl&&(O(Te,P),O(Oe,U),O(Oe,z))),t.ADD_TAGS&&(Te===Ce&&(Te=A(Te)),O(Te,t.ADD_TAGS,ve)),t.ADD_ATTR&&(Oe===Ae&&(Oe=A(Oe)),O(Oe,t.ADD_ATTR,ve)),t.ADD_URI_SAFE_ATTR&&O(tt,t.ADD_URI_SAFE_ATTR,ve),t.FORBID_CONTENTS&&(Ze===Je&&(Ze=A(Ze)),O(Ze,t.FORBID_CONTENTS,ve)),Ye&&(Te["#text"]=!0),Be&&O(Te,["html","head","body"]),Te.table&&(O(Te,["tbody"]),delete Re.tbody),m&&m(t),ut=t)},ht=O({},["mi","mo","mn","ms","mtext"]),gt=O({},["annotation-xml"]),vt=O({},["title","style","font","a","script"]),Et=O({},D);O(Et,M),O(Et,I);var yt=O({},P);O(yt,j);var xt=function(e){y(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=se}catch(t){e.remove()}}},bt=function(e,t){try{y(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){y(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!Oe[e])if(Ge||Ve)try{xt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},wt=function(e){var t,n;if(ze)e="<remove></remove>"+e;else{var r=w(e,/^[\r\n\t ]+/);n=r&&r[0]}"application/xhtml+xml"===ge&&at===it&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var o=ae?ae.createHTML(e):e;if(at===it)try{t=(new g).parseFromString(o,ge)}catch(e){}if(!t||!t.documentElement){t=ce.createDocument(at,"template",null);try{t.documentElement.innerHTML=st?se:o}catch(e){}}var i=t.body||t.documentElement;return e&&n&&i.insertBefore(a.createTextNode(n),i.childNodes[0]||null),at===it?pe.call(t,Be?"html":"body")[0]:Be?t.documentElement:i},_t=function(e){return me.call(e.ownerDocument||e,e,p.SHOW_ELEMENT|p.SHOW_COMMENT|p.SHOW_TEXT|p.SHOW_PROCESSING_INSTRUCTION|p.SHOW_CDATA_SECTION,null,!1)},Nt=function(e){return e instanceof h&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof f)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},kt=function(t){return"object"===e(c)?t instanceof c:t&&"object"===e(t)&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName},St=function(e,t,n){he[e]&&v(he[e],(function(e){e.call(o,t,n,ut)}))},Tt=function(e){var t;if(St("beforeSanitizeElements",e,null),Nt(e))return xt(e),!0;if(S(/[\u0080-\uFFFF]/,e.nodeName))return xt(e),!0;var n=ve(e.nodeName);if(St("uponSanitizeElement",e,{tagName:n,allowedTags:Te}),e.hasChildNodes()&&!kt(e.firstElementChild)&&(!kt(e.content)||!kt(e.content.firstElementChild))&&S(/<[/\w]/g,e.innerHTML)&&S(/<[/\w]/g,e.textContent))return xt(e),!0;if("select"===n&&S(/<template/i,e.innerHTML))return xt(e),!0;if(7===e.nodeType)return xt(e),!0;if(He&&8===e.nodeType&&S(/<[/\w]/g,e.data))return xt(e),!0;if(!Te[n]||Re[n]){if(!Re[n]&&Ot(n)){if(Le.tagNameCheck instanceof RegExp&&S(Le.tagNameCheck,n))return!1;if(Le.tagNameCheck instanceof Function&&Le.tagNameCheck(n))return!1}if(Ye&&!Ze[n]){var r=oe(e)||e.parentNode,i=re(e)||e.childNodes;if(i&&r)for(var a=i.length-1;a>=0;--a){var s=te(i[a],!0);s.__removalCount=(e.__removalCount||0)+1,r.insertBefore(s,ne(e))}}return xt(e),!0}return e instanceof u&&!function(e){var t=oe(e);t&&t.tagName||(t={namespaceURI:at,tagName:"template"});var n=x(e.tagName),r=x(t.tagName);return!!lt[e.namespaceURI]&&(e.namespaceURI===ot?t.namespaceURI===it?"svg"===n:t.namespaceURI===rt?"svg"===n&&("annotation-xml"===r||ht[r]):Boolean(Et[n]):e.namespaceURI===rt?t.namespaceURI===it?"math"===n:t.namespaceURI===ot?"math"===n&&gt[r]:Boolean(yt[n]):e.namespaceURI===it?!(t.namespaceURI===ot&&!gt[r])&&!(t.namespaceURI===rt&&!ht[r])&&!yt[n]&&(vt[n]||!Et[n]):!("application/xhtml+xml"!==ge||!lt[e.namespaceURI]))}(e)?(xt(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!S(/<\/no(script|embed|frames)/i,e.innerHTML)?(Fe&&3===e.nodeType&&(t=e.textContent,t=_(t,Ee," "),t=_(t,ye," "),t=_(t,xe," "),e.textContent!==t&&(y(o.removed,{element:e.cloneNode()}),e.textContent=t)),St("afterSanitizeElements",e,null),!1):(xt(e),!0)},Ct=function(e,t,n){if($e&&("id"===t||"name"===t)&&(n in a||n in pt))return!1;if(Ie&&!De[t]&&S(be,t));else if(Me&&S(we,t));else if(!Oe[t]||De[t]){if(!(Ot(e)&&(Le.tagNameCheck instanceof RegExp&&S(Le.tagNameCheck,e)||Le.tagNameCheck instanceof Function&&Le.tagNameCheck(e))&&(Le.attributeNameCheck instanceof RegExp&&S(Le.attributeNameCheck,t)||Le.attributeNameCheck instanceof Function&&Le.attributeNameCheck(t))||"is"===t&&Le.allowCustomizedBuiltInElements&&(Le.tagNameCheck instanceof RegExp&&S(Le.tagNameCheck,n)||Le.tagNameCheck instanceof Function&&Le.tagNameCheck(n))))return!1}else if(tt[t]);else if(S(Se,_(n,Ne,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==N(n,"data:")||!Qe[e])if(Pe&&!S(_e,_(n,Ne,"")));else if(n)return!1;return!0},Ot=function(e){return"annotation-xml"!==e&&w(e,ke)},At=function(t){var n,r,i,a;St("beforeSanitizeAttributes",t,null);var s=t.attributes;if(s){var l={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Oe};for(a=s.length;a--;){var c=n=s[a],m=c.name,u=c.namespaceURI;if(r="value"===m?n.value:k(n.value),i=ve(m),l.attrName=i,l.attrValue=r,l.keepAttr=!0,l.forceKeepAttr=void 0,St("uponSanitizeAttribute",t,l),r=l.attrValue,!l.forceKeepAttr&&(bt(m,t),l.keepAttr))if(je||!S(/\/>/i,r)){Fe&&(r=_(r,Ee," "),r=_(r,ye," "),r=_(r,xe," "));var p=ve(t.nodeName);if(Ct(p,i,r))if(!qe||"id"!==i&&"name"!==i||(bt(m,t),r="user-content-"+r),He&&S(/((--!?|])>)|<\/(style|title)/i,r))bt(m,t);else{if(ae&&"object"===e(C)&&"function"==typeof C.getAttributeType)if(u);else switch(C.getAttributeType(p,i)){case"TrustedHTML":r=ae.createHTML(r);break;case"TrustedScriptURL":r=ae.createScriptURL(r)}try{u?t.setAttributeNS(u,m,r):t.setAttribute(m,r),Nt(t)?xt(t):E(o.removed)}catch(e){}}}else bt(m,t)}St("afterSanitizeAttributes",t,null)}},Lt=function e(t){var n,r=_t(t);for(St("beforeSanitizeShadowDOM",t,null);n=r.nextNode();)St("uponSanitizeShadowNode",n,null),Tt(n)||(n.content instanceof s&&e(n.content),At(n));St("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(t){var r,a,l,m,u,p=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if((st=!t)&&(t="\x3c!--\x3e"),"string"!=typeof t&&!kt(t)){if("function"!=typeof t.toString)throw T("toString is not a function");if("string"!=typeof(t=t.toString()))throw T("dirty is not a string, aborting")}if(!o.isSupported){if("object"===e(n.toStaticHTML)||"function"==typeof n.toStaticHTML){if("string"==typeof t)return n.toStaticHTML(t);if(kt(t))return n.toStaticHTML(t.outerHTML)}return t}if(Ue||ft(p),o.removed=[],"string"==typeof t&&(Ke=!1),Ke){if(t.nodeName){var d=ve(t.nodeName);if(!Te[d]||Re[d])throw T("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof c)1===(a=(r=wt("\x3c!----\x3e")).ownerDocument.importNode(t,!0)).nodeType&&"BODY"===a.nodeName||"HTML"===a.nodeName?r=a:r.appendChild(a);else{if(!Ge&&!Fe&&!Be&&-1===t.indexOf("<"))return ae&&We?ae.createHTML(t):t;if(!(r=wt(t)))return Ge?null:We?se:""}r&&ze&&xt(r.firstChild);for(var f=_t(Ke?t:r);l=f.nextNode();)3===l.nodeType&&l===m||Tt(l)||(l.content instanceof s&&Lt(l.content),At(l),m=l);if(m=null,Ke)return t;if(Ge){if(Ve)for(u=ue.call(r.ownerDocument);r.firstChild;)u.appendChild(r.firstChild);else u=r;return(Oe.shadowroot||Oe.shadowrootmod)&&(u=de.call(i,u,!0)),u}var h=Be?r.outerHTML:r.innerHTML;return Be&&Te["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&S(Z,r.ownerDocument.doctype.name)&&(h="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+h),Fe&&(h=_(h,Ee," "),h=_(h,ye," "),h=_(h,xe," ")),ae&&We?ae.createHTML(h):h},o.setConfig=function(e){ft(e),Ue=!0},o.clearConfig=function(){ut=null,Ue=!1},o.isValidAttribute=function(e,t,n){ut||ft({});var r=ve(e),o=ve(t);return Ct(r,o,n)},o.addHook=function(e,t){"function"==typeof t&&(he[e]=he[e]||[],y(he[e],t))},o.removeHook=function(e){if(he[e])return E(he[e])},o.removeHooks=function(e){he[e]&&(he[e]=[])},o.removeAllHooks=function(){he={}},o}()}()},94736:(e,t,n)=>{"use strict";t.A=function(e){var t=e.size,n=void 0===t?24:t,r=e.onClick,s=(e.icon,e.className),l=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],0<=t.indexOf(n)||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,i),c=["gridicon","gridicons-notice-outline",s,!!function(e){return 0==e%18}(n)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return o.default.createElement("svg",a({className:c,height:n,width:n,onClick:r},l,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),o.default.createElement("g",null,o.default.createElement("path",{d:"M12 4c4.411 0 8 3.589 8 8s-3.589 8-8 8-8-3.589-8-8 3.589-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-2-2h2l.5-6h-3l.5 6z"})))};var r,o=(r=n(51609))&&r.__esModule?r:{default:r},i=["size","onClick","icon","className"];function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},a.apply(this,arguments)}},94931:(e,t,n)=>{"use strict";var r=n(51609),o=Symbol.for("react.element"),i=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,n){var r,l={},c=null,m=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(m=t.ref),t)i.call(t,r)&&!s.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:o,type:e,key:c,ref:m,props:l,_owner:a.current}}},39793:(e,t,n)=>{"use strict";e.exports=n(94931)},51609:e=>{"use strict";e.exports=window.React}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";n.r(r),n.d(r,{CollapsibleList:()=>V,InboxDismissConfirmationModal:()=>de,InboxNoteCard:()=>pe,InboxNotePlaceholder:()=>fe,List:()=>P,ListItem:()=>O,Navigation:()=>he,NavigationBackButton:()=>ge,NavigationGroup:()=>ve,NavigationItem:()=>ye,NavigationMenu:()=>Ee,TaskItem:()=>ne,Text:()=>xe,VerticalCSSTransition:()=>X,useSlot:()=>we});const e=window.wp.components;function t(){return t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},t.apply(null,arguments)}function o(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}function i(e,t){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},i(e,t)}function a(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,i(e,t)}function s(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}var l=n(51609),c=n.n(l);const m=window.ReactDOM;var u=n.n(m);const p=c().createContext(null);var d=function(e){return e.scrollTop},f="unmounted",h="exited",g="entering",v="entered",E="exiting",y=function(e){function t(t,n){var r;r=e.call(this,t,n)||this;var o,i=n&&!n.isMounting?t.enter:t.appear;return r.appearStatus=null,t.in?i?(o=h,r.appearStatus=g):o=v:o=t.unmountOnExit||t.mountOnEnter?f:h,r.state={status:o},r.nextCallback=null,r}a(t,e),t.getDerivedStateFromProps=function(e,t){return e.in&&t.status===f?{status:h}:null};var n=t.prototype;return n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==g&&n!==v&&(t=g):n!==g&&n!==v||(t=E)}this.updateStatus(!1,t)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!=typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},n.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===g){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:u().findDOMNode(this);n&&d(n)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===h&&this.setState({status:f})},n.performEnter=function(e){var t=this,n=this.props.enter,r=this.context?this.context.isMounting:e,o=this.props.nodeRef?[r]:[u().findDOMNode(this),r],i=o[0],a=o[1],s=this.getTimeouts(),l=r?s.appear:s.enter;e||n?(this.props.onEnter(i,a),this.safeSetState({status:g},(function(){t.props.onEntering(i,a),t.onTransitionEnd(l,(function(){t.safeSetState({status:v},(function(){t.props.onEntered(i,a)}))}))}))):this.safeSetState({status:v},(function(){t.props.onEntered(i)}))},n.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),r=this.props.nodeRef?void 0:u().findDOMNode(this);t?(this.props.onExit(r),this.safeSetState({status:E},(function(){e.props.onExiting(r),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:h},(function(){e.props.onExited(r)}))}))}))):this.safeSetState({status:h},(function(){e.props.onExited(r)}))},n.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},n.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},n.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:u().findDOMNode(this),r=null==e&&!this.props.addEndListener;if(n&&!r){if(this.props.addEndListener){var o=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],i=o[0],a=o[1];this.props.addEndListener(i,a)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},n.render=function(){var e=this.state.status;if(e===f)return null;var t=this.props,n=t.children,r=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,o(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return c().createElement(p.Provider,{value:null},"function"==typeof n?n(e,r):c().cloneElement(c().Children.only(n),r))},t}(c().Component);function x(){}y.contextType=p,y.propTypes={},y.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:x,onEntering:x,onEntered:x,onExit:x,onExiting:x,onExited:x},y.UNMOUNTED=f,y.EXITED=h,y.ENTERING=g,y.ENTERED=v,y.EXITING=E;const b=y;var w=function(e,t){return e&&t&&t.split(" ").forEach((function(t){return r=t,void((n=e).classList?n.classList.remove(r):"string"==typeof n.className?n.className=s(n.className,r):n.setAttribute("class",s(n.className&&n.className.baseVal||"",r)));var n,r}))},_=function(e){function n(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(t=e.call.apply(e,[this].concat(r))||this).appliedClasses={appear:{},enter:{},exit:{}},t.onEnter=function(e,n){var r=t.resolveArguments(e,n),o=r[0],i=r[1];t.removeClasses(o,"exit"),t.addClass(o,i?"appear":"enter","base"),t.props.onEnter&&t.props.onEnter(e,n)},t.onEntering=function(e,n){var r=t.resolveArguments(e,n),o=r[0],i=r[1]?"appear":"enter";t.addClass(o,i,"active"),t.props.onEntering&&t.props.onEntering(e,n)},t.onEntered=function(e,n){var r=t.resolveArguments(e,n),o=r[0],i=r[1]?"appear":"enter";t.removeClasses(o,i),t.addClass(o,i,"done"),t.props.onEntered&&t.props.onEntered(e,n)},t.onExit=function(e){var n=t.resolveArguments(e)[0];t.removeClasses(n,"appear"),t.removeClasses(n,"enter"),t.addClass(n,"exit","base"),t.props.onExit&&t.props.onExit(e)},t.onExiting=function(e){var n=t.resolveArguments(e)[0];t.addClass(n,"exit","active"),t.props.onExiting&&t.props.onExiting(e)},t.onExited=function(e){var n=t.resolveArguments(e)[0];t.removeClasses(n,"exit"),t.addClass(n,"exit","done"),t.props.onExited&&t.props.onExited(e)},t.resolveArguments=function(e,n){return t.props.nodeRef?[t.props.nodeRef.current,e]:[e,n]},t.getClassNames=function(e){var n=t.props.classNames,r="string"==typeof n,o=r?(r&&n?n+"-":"")+e:n[e];return{baseClassName:o,activeClassName:r?o+"-active":n[e+"Active"],doneClassName:r?o+"-done":n[e+"Done"]}},t}a(n,e);var r=n.prototype;return r.addClass=function(e,t,n){var r=this.getClassNames(t)[n+"ClassName"],o=this.getClassNames("enter").doneClassName;"appear"===t&&"done"===n&&o&&(r+=" "+o),"active"===n&&e&&d(e),r&&(this.appliedClasses[t][n]=r,function(e,t){e&&t&&t.split(" ").forEach((function(t){return r=t,void((n=e).classList?n.classList.add(r):function(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}(n,r)||("string"==typeof n.className?n.className=n.className+" "+r:n.setAttribute("class",(n.className&&n.className.baseVal||"")+" "+r)));var n,r}))}(e,r))},r.removeClasses=function(e,t){var n=this.appliedClasses[t],r=n.base,o=n.active,i=n.done;this.appliedClasses[t]={},r&&w(e,r),o&&w(e,o),i&&w(e,i)},r.render=function(){var e=this.props,n=(e.classNames,o(e,["classNames"]));return c().createElement(b,t({},n,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},n}(c().Component);_.defaultProps={classNames:""},_.propTypes={};const N=_,k=window.wp.element,S=window.wp.keycodes;var T=n(17697),C=n.n(T);const O=({children:e,disableGutters:t=!1,animation:n="none",className:r="",exit:o,enter:i,onExited:a,in:s,...l})=>{const c=!!l?.onClick,m=c?{role:"button",onKeyDown:e=>{return t=e,void("function"==typeof(n=l.onClick)&&t.keyCode===S.ENTER&&n(t));var t,n},tabIndex:0}:{},u=C()({"has-action":c,"has-gutters":!t,"transitions-disabled":"slide-right"!==n});return(0,k.createElement)(N,{timeout:500,classNames:r||"woocommerce-list__item",in:s,exit:o,enter:i,onExited:a},(0,k.createElement)("li",{...m,...l,className:`woocommerce-experimental-list__item ${u} ${r}`},e))};function A(e,t){var n=Object.create(null);return e&&l.Children.map(e,(function(e){return e})).forEach((function(e){n[e.key]=function(e){return t&&(0,l.isValidElement)(e)?t(e):e}(e)})),n}function L(e,t,n){return null!=n[t]?n[t]:e.props[t]}function R(e,t,n){var r=A(e.children),o=function(e,t){function n(n){return n in t?t[n]:e[n]}e=e||{},t=t||{};var r,o=Object.create(null),i=[];for(var a in e)a in t?i.length&&(o[a]=i,i=[]):i.push(a);var s={};for(var l in t){if(o[l])for(r=0;r<o[l].length;r++){var c=o[l][r];s[o[l][r]]=n(c)}s[l]=n(l)}for(r=0;r<i.length;r++)s[i[r]]=n(i[r]);return s}(t,r);return Object.keys(o).forEach((function(i){var a=o[i];if((0,l.isValidElement)(a)){var s=i in t,c=i in r,m=t[i],u=(0,l.isValidElement)(m)&&!m.props.in;!c||s&&!u?c||!s||u?c&&s&&(0,l.isValidElement)(m)&&(o[i]=(0,l.cloneElement)(a,{onExited:n.bind(null,a),in:m.props.in,exit:L(a,"exit",e),enter:L(a,"enter",e)})):o[i]=(0,l.cloneElement)(a,{in:!1}):o[i]=(0,l.cloneElement)(a,{onExited:n.bind(null,a),in:!0,exit:L(a,"exit",e),enter:L(a,"enter",e)})}})),o}var D=Object.values||function(e){return Object.keys(e).map((function(t){return e[t]}))},M=function(e){function n(t,n){var r,o=(r=e.call(this,t,n)||this).handleExited.bind(function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(r));return r.state={contextValue:{isMounting:!0},handleExited:o,firstRender:!0},r}a(n,e);var r=n.prototype;return r.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},r.componentWillUnmount=function(){this.mounted=!1},n.getDerivedStateFromProps=function(e,t){var n,r,o=t.children,i=t.handleExited;return{children:t.firstRender?(n=e,r=i,A(n.children,(function(e){return(0,l.cloneElement)(e,{onExited:r.bind(null,e),in:!0,appear:L(e,"appear",n),enter:L(e,"enter",n),exit:L(e,"exit",n)})}))):R(e,o,i),firstRender:!1}},r.handleExited=function(e,n){var r=A(this.props.children);e.key in r||(e.props.onExited&&e.props.onExited(n),this.mounted&&this.setState((function(n){var r=t({},n.children);return delete r[e.key],{children:r}})))},r.render=function(){var e=this.props,t=e.component,n=e.childFactory,r=o(e,["component","childFactory"]),i=this.state.contextValue,a=D(this.state.children).map(n);return delete r.appear,delete r.enter,delete r.exit,null===t?c().createElement(p.Provider,{value:i},a):c().createElement(p.Provider,{value:i},c().createElement(t,r,a))},n}(c().Component);M.propTypes={},M.defaultProps={component:"div",childFactory:function(e){return e}};const I=M,P=({children:e,listType:t,animation:n="none",...r})=>(0,k.createElement)(I,{component:t||"ul",className:"woocommerce-experimental-list",...r},k.Children.map(e,(e=>{if((0,k.isValidElement)(e)){const{onExited:t,in:r,enter:o,exit:i,...a}=e.props,s=a.animation||n;return(0,k.createElement)(N,{timeout:500,onExited:t,in:r,enter:o,exit:i,classNames:"woocommerce-list__item"},(0,k.cloneElement)(e,{animation:s,...a}))}return e}))),j=(0,k.forwardRef)((function({icon:e,size:t=24,...n},r){return(0,k.cloneElement)(e,{width:t,height:t,...n,ref:r})})),F=window.wp.primitives;var H=n(39793);const B=(0,H.jsx)(F.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,H.jsx)(F.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})}),U=(0,H.jsx)(F.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,H.jsx)(F.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})}),z={transitionProperty:"max-height",transitionDuration:"500ms",maxHeight:0,overflow:"hidden"};function G(e,t,n){if(n.length<e.length){const e=n.map((e=>e.key));return t.filter((t=>t.key&&e.includes(t.key)))}const r=t.map((e=>e.key)),o=e.map((e=>e.key));return n.filter((e=>e.key&&(r.includes(e.key)||!o.includes(e.key))))}const V=({children:e,collapsed:t=!0,collapseLabel:n,expandLabel:r,show:o=0,onCollapse:i,onExpand:a,direction:s="up",...l})=>{const[c,m]=(0,k.useState)(t),[u,p]=(0,k.useState)(t),[d,f]=(0,k.useState)({collapse:n,expand:r}),[h,g]=(0,k.useState)({all:[],shown:[],hidden:[]}),v=(0,k.useRef)(null),E=()=>{let t=[];const i=k.Children.map(e,(e=>(0,k.isValidElement)(e)&&"key"in e?e:null))||[];let a=i;o>0&&(t=i.slice(0,o),a=i.slice(o)),a.length>0&&f({expand:r,collapse:n}),g({all:i,shown:t,hidden:a})};(0,k.useEffect)((()=>{p(c)}),[c]),(0,k.useEffect)((()=>{const t=k.Children.map(e,(e=>(0,k.isValidElement)(e)&&"key"in e?e:null))||[];h.all.length>0&&c&&"none"!==l.animation?(g({...h,shown:G(h.all,h.shown,t)}),setTimeout((()=>{E()}),500)):E()}),[e]);const y=(0,k.useCallback)((()=>{var e;m(!c),e=!c,i&&e&&i(),a&&!e&&a()}),[c]),x=C()(l.className||"","woocommerce-experimental-list"),w=C()({"woocommerce-experimental-list-wrapper":!c}),_=h.hidden.length>0?(0,k.createElement)(O,{key:"collapse-item",className:"list-item-collapse",onClick:y,animation:"none",disableGutters:!0},(0,k.createElement)("p",null,c?d.expand:d.collapse),(0,k.createElement)(j,{className:"list-item-collapse__icon",size:30,icon:c?B:U})):null;return(0,k.createElement)(P,{...l,className:x},["down"===s&&_,...h.shown,(0,k.createElement)(b,{key:"remaining-children",timeout:500,in:!u,mountOnEnter:!0,unmountOnExit:!1},(e=>{const t=((e,t,n)=>{let r=0;"entered"!==e&&"entering"!==e||!n||(r=function(e){let t=0;if(e)for(const n of e.children){t+=n.clientHeight;const e=window.getComputedStyle(n);t+=parseInt(e.marginTop,10)||0,t+=parseInt(e.marginBottom,10)||0}return t}(n));const o={...z,maxHeight:r};return"entering"!==e&&"exiting"!==e&&(delete o.transitionDuration,delete o.transition,delete o.transitionProperty),"entered"!==e||t||delete o.maxHeight,o})(e,c,v.current);return(0,k.createElement)("div",{className:w,ref:v,style:t},(0,k.createElement)(I,{className:"woocommerce-experimental-list"},k.Children.map(h.hidden,(e=>{const{onExited:t,in:n,enter:r,exit:o,...i}=e.props,a=i.animation||l.animation;return(0,k.createElement)(N,{key:e.key,timeout:500,onExited:t,in:n,enter:r,exit:o,classNames:"woocommerce-list__item"},(0,k.cloneElement)(e,{animation:a,...i}))}))))})),"up"===s&&_])},W=window.wp.i18n,$=(0,H.jsx)(F.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,H.jsx)(F.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})});var q=n(94736);const Y=window.wc.components;var K=n(13240);const X=({children:e,defaultStyle:t,...n})=>{const[r,o]=(0,k.useState)(0),[i,a]=(0,k.useState)(n.in||!1),s=(0,k.useRef)(null),l=(0,k.useCallback)((e=>{e&&o(function(e){let t=0;for(const n of e.children){t+=n.clientHeight;const e=window.getComputedStyle(n);t+=parseInt(e.marginTop,10)||0,t+=parseInt(e.marginBottom,10)||0}return t}(e))}),[e]);(0,k.useEffect)((()=>{a(n.in||!1)}),[n.in]);const c={entered:{maxHeight:r},entering:{maxHeight:r},exiting:{maxHeight:0},exited:{maxHeight:0}},m=e=>{const r=(()=>{const{timeout:e}=n;let t,r,o;return"number"==typeof e&&(t=r=o=e),void 0!==e&&"number"!=typeof e&&(t=e.exit,r=e.enter,o=void 0!==e.appear?e.appear:r),{exit:t,enter:r,appear:o}})(),o=s.current&&s.current.context&&s.current.context.isMounting;let i;i=e.startsWith("enter")?r[o?"enter":"appear"]:r.exit;const a={transitionProperty:"max-height",transitionDuration:void 0===i?"500ms":i+"ms",overflow:"hidden",...t||{},...e in c?c[e]:{}};return"entering"!==e&&"exiting"!==e&&(delete a.transitionDuration,delete a.transition,delete a.transitionProperty),"entered"===e&&n.in&&delete a.maxHeight,a};return(0,k.createElement)(N,{...n,in:i,ref:s},(t=>(0,k.createElement)("div",{className:"vertical-css-transition-container",style:m(t),ref:l},e)))},Z=["a","b","em","i","strong","p","br"],J=["target","href","rel","name","download"],Q=e=>({__html:(0,K.sanitize)(e,{ALLOWED_TAGS:Z,ALLOWED_ATTR:J})}),ee=({level:t,completed:n,children:r})=>{let o="";return 1!==t||n?2!==t||n||(o=(0,W.__)("This task is required to set up your extension","woocommerce")):o=(0,W.__)("This task is required to keep your store running","woocommerce"),""===o?r:(0,k.createElement)(e.Tooltip,{text:o},r)},te=({children:e,expandable:t,expanded:n})=>t?(0,k.createElement)(X,{timeout:500,in:n,classNames:"woocommerce-task-list__item-expandable-content",defaultStyle:{transitionProperty:"max-height, opacity"}},e):n?(0,k.createElement)(k.Fragment,null,e):null,ne=({completed:t,title:n,badge:r,onDelete:o,onCollapse:i,onDismiss:a,onSnooze:s,onExpand:l,onClick:c,additionalInfo:m,time:u,content:p,expandable:d=!1,expanded:f=!1,showActionButton:h,level:g=3,action:v,actionLabel:E,...y})=>{const[x,b]=(0,k.useState)(f);(0,k.useEffect)((()=>{b(f)}),[f]);const w=C()("woocommerce-task-list__item",{complete:t,expanded:x,"level-2":2===g&&!t,"level-1":1===g&&!t});void 0===h&&(h=d);const _=(a||s)&&!t||o&&t;return(0,k.createElement)(O,{disableGutters:!0,className:w,onClick:d&&h?()=>{b(!x),x&&l&&l(),!x&&i&&i()}:c,...y},(0,k.createElement)(ee,{level:g,completed:t},(0,k.createElement)("div",{className:"woocommerce-task-list__item-before"},1!==g||t?(0,k.createElement)("div",{className:"woocommerce-task__icon"},t&&(0,k.createElement)(j,{icon:$})):(0,k.createElement)(q.A,{size:36}))),(0,k.createElement)("div",{className:"woocommerce-task-list__item-text"},(0,k.createElement)(xe,{as:"div",size:"14",lineHeight:t?"18px":"20px",weight:t?"normal":"600",variant:t?"body.small":"button"},(0,k.createElement)("span",{className:"woocommerce-task-list__item-title"},n,r&&(0,k.createElement)("span",{className:"woocommerce-task-list__item-badge"},r)),(0,k.createElement)(te,{expandable:d,expanded:x},(0,k.createElement)("div",{className:"woocommerce-task-list__item-expandable-content"},p,d&&!t&&m&&(0,k.createElement)("div",{className:"woocommerce-task__additional-info",dangerouslySetInnerHTML:Q(m)}),!t&&h&&(0,k.createElement)(e.Button,{className:"woocommerce-task-list__item-action",isPrimary:!0,onClick:e=>{e.stopPropagation(),v(e,{isExpanded:!0})}},E||n))),!d&&!t&&m&&(0,k.createElement)("div",{className:"woocommerce-task__additional-info",dangerouslySetInnerHTML:Q(m)}),u&&(0,k.createElement)("div",{className:"woocommerce-task__estimated-time"},u))),_&&(0,k.createElement)(Y.EllipsisMenu,{label:(0,W.__)("Task Options","woocommerce"),className:"woocommerce-task-list__item-after",onToggle:e=>e.stopPropagation(),renderContent:()=>(0,k.createElement)("div",{className:"woocommerce-task-card__section-controls"},a&&!t&&(0,k.createElement)(e.Button,{onClick:e=>{e.stopPropagation(),a()}},(0,W.__)("Dismiss","woocommerce")),s&&!t&&(0,k.createElement)(e.Button,{onClick:e=>{e.stopPropagation(),s()}},(0,W.__)("Remind me later","woocommerce")),o&&t&&(0,k.createElement)(e.Button,{onClick:e=>{e.stopPropagation(),o()}},(0,W.__)("Delete","woocommerce")))}))};Object.defineProperty;var re=new Map,oe=new WeakMap,ie=0;function ae(e,t,n={},r=undefined){if(void 0===window.IntersectionObserver&&void 0!==r){const o=e.getBoundingClientRect();return t(r,{isIntersecting:r,target:e,intersectionRatio:"number"==typeof n.threshold?n.threshold:0,time:0,boundingClientRect:o,intersectionRect:o,rootBounds:o}),()=>{}}const{id:o,observer:i,elements:a}=function(e){const t=function(e){return Object.keys(e).sort().filter((t=>void 0!==e[t])).map((t=>{return`${t}_${"root"===t?(n=e.root,n?(oe.has(n)||(ie+=1,oe.set(n,ie.toString())),oe.get(n)):"0"):e[t]}`;var n})).toString()}(e);let n=re.get(t);if(!n){const r=new Map;let o;const i=new IntersectionObserver((t=>{t.forEach((t=>{var n;const i=t.isIntersecting&&o.some((e=>t.intersectionRatio>=e));e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=i),null==(n=r.get(t.target))||n.forEach((e=>{e(i,t)}))}))}),e);o=i.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:i,elements:r},re.set(t,n)}return n}(n),s=a.get(e)||[];return a.has(e)||a.set(e,s),s.push(t),i.observe(e),function(){s.splice(s.indexOf(t),1),0===s.length&&(a.delete(e),i.unobserve(e)),0===a.size&&(i.disconnect(),re.delete(o))}}l.Component;const se=window.moment;var le=n.n(se);const ce=({label:t,onClick:n,href:r,preventBusyState:o,variant:i="link"})=>{const[a,s]=(0,k.useState)(!1);return(0,k.createElement)(e.Button,{className:"woocommerce-inbox-note__action-button",variant:i,isBusy:a,disabled:a,href:r||void 0,onClick:e=>{const t=e.currentTarget&&"href"in e.currentTarget?e.currentTarget.href:"";let r=!0,i="";window.wcSettings&&(i=window.wcSettings.adminUrl),!t.length||i&&t.startsWith(i)||(e.preventDefault(),r=!1,window.open(t,"_blank")),o&&(r=!1),s(r),n()}},(0,k.createElement)("span",null,t))},me=["a","b","em","i","strong","p","br"],ue=["target","href","rel","name","download"],pe=({note:t,onDismiss:n,onNoteActionClick:r,onBodyLinkClick:o,onNoteVisible:i,className:a})=>{const[s,c]=(0,k.useState)(!1),{ref:m}=function({threshold:e,delay:t,trackVisibility:n,rootMargin:r,root:o,triggerOnce:i,skip:a,initialInView:s,fallbackInView:c,onChange:m}={}){var u;const[p,d]=l.useState(null),f=l.useRef(m),[h,g]=l.useState({inView:!!s,entry:void 0});f.current=m,l.useEffect((()=>{if(a||!p)return;let s;return s=ae(p,((e,t)=>{g({inView:e,entry:t}),f.current&&f.current(e,t),t.isIntersecting&&i&&s&&(s(),s=void 0)}),{root:o,rootMargin:r,threshold:e,trackVisibility:n,delay:t},c),()=>{s&&s()}}),[Array.isArray(e)?e.toString():e,p,o,r,i,a,n,c,t]);const v=null==(u=h.entry)?void 0:u.target,E=l.useRef(void 0);p||!v||i||a||E.current===v||(E.current=v,g({inView:!!s,entry:void 0}));const y=[d,h.inView,h.entry];return y.ref=y[0],y.inView=y[1],y.entry=y[2],y}({triggerOnce:!0,threshold:1,onChange:e=>{e&&i&&i(t)}}),u=function(e){const t=(0,k.useCallback)((t=>{const n=t.target;if(n&&"href"in n){const t=n.href;t&&e&&e(t)}}),[e]),n=(0,k.useRef)(null);return(0,k.useEffect)((()=>{const e=n.current;return e&&e.addEventListener("click",t),()=>{e&&e.removeEventListener("click",t)}}),[t]),(0,k.useCallback)((e=>{n.current=e}),[])}((e=>{o&&o(t,e)})),p=e=>{r&&r(t,e),e.actioned_text&&c(e.actioned_text)},{content:d,date_created_gmt:f,image:h,is_deleted:g,layout:v,status:E,title:y,is_read:x}=t;if(g)return null;const b=!1===x,w="thumbnail"===v,_=C()("woocommerce-inbox-message",a,v,{"message-is-unread":b&&"unactioned"===E}),N=C()("woocommerce-inbox-message__actions",{"has-multiple-actions":t.actions?.length>1});return(0,k.createElement)("section",{ref:m,className:_},w&&(0,k.createElement)("div",{className:"woocommerce-inbox-message__image"},(0,k.createElement)("img",{src:h,alt:""})),(0,k.createElement)("div",{className:"woocommerce-inbox-message__wrapper"},(0,k.createElement)("div",{className:"woocommerce-inbox-message__content"},b&&(0,k.createElement)("div",{className:"woocommerce-inbox-message__unread-indicator"}),f&&(0,k.createElement)("span",{className:"woocommerce-inbox-message__date"},le().utc(f).fromNow()),(0,k.createElement)(Y.H,{className:"woocommerce-inbox-message__title"},t.actions&&1===t.actions.length&&(0,k.createElement)(ce,{key:t.actions[0].id,label:y,preventBusyState:!0,variant:"link",href:t.actions[0].url&&t.actions[0].url.length?t.actions[0].url:void 0,onClick:()=>p(t.actions[0])}),t.actions&&t.actions.length>1&&y),(0,k.createElement)(Y.Section,{className:"woocommerce-inbox-message__text"},(0,k.createElement)("span",{dangerouslySetInnerHTML:(S=d,{__html:(0,K.sanitize)(S,{ALLOWED_TAGS:me,ALLOWED_ATTR:ue})}),ref:u}))),(0,k.createElement)("div",{className:N},(()=>{const{actions:e}=t;return s||(e?(0,k.createElement)(k.Fragment,null,e.map((e=>(0,k.createElement)(ce,{key:e.id,label:e.label,variant:"secondary",href:e&&e.url&&e.url.length?e.url:void 0,onClick:()=>p(e)})))):void 0)})(),s?null:(0,k.createElement)(e.Button,{className:"woocommerce-admin-dismiss-notification",onClick:()=>n&&n(t)},(0,W.__)("Dismiss","woocommerce")))));var S},de=({onClose:t,onDismiss:n,buttonLabel:r=(0,W.__)("Yes, I'm sure","woocommerce")})=>{const[o,i]=(0,k.useState)(!1);return(0,k.createElement)(e.Modal,{title:(0,W.__)("Are you sure?","woocommerce"),onRequestClose:()=>t(),className:"woocommerce-inbox-dismiss-confirmation_modal"},(0,k.createElement)("div",{className:"woocommerce-inbox-dismiss-confirmation_wrapper"},(0,k.createElement)("p",null,(0,W.__)("Dismissed messages cannot be viewed again","woocommerce")),(0,k.createElement)("div",{className:"woocommerce-inbox-dismiss-confirmation_buttons"},(0,k.createElement)(e.Button,{isSecondary:!0,onClick:()=>t()},(0,W.__)("Cancel","woocommerce")),(0,k.createElement)(e.Button,{isSecondary:!0,isBusy:o,disabled:o,onClick:()=>{i(!0),n()}},r))))},fe=({className:e})=>(0,k.createElement)("div",{className:`woocommerce-inbox-message is-placeholder ${e}`,"aria-hidden":!0},(0,k.createElement)("div",{className:"woocommerce-inbox-message__wrapper"},(0,k.createElement)("div",{className:"woocommerce-inbox-message__content"},(0,k.createElement)("div",{className:"woocommerce-inbox-message__date"},(0,k.createElement)("div",{className:"sixth-line"})),(0,k.createElement)("div",{className:"woocommerce-inbox-message__title"},(0,k.createElement)("div",{className:"line"}),(0,k.createElement)("div",{className:"line"})),(0,k.createElement)("div",{className:"woocommerce-inbox-message__text"},(0,k.createElement)("div",{className:"line"}),(0,k.createElement)("div",{className:"third-line"}))),(0,k.createElement)("div",{className:"woocommerce-inbox-message__actions"},(0,k.createElement)("div",{className:"fifth-line"}),(0,k.createElement)("div",{className:"fifth-line"})))),he=e.Navigation||e.__experimentalNavigation,ge=e.NavigationBackButton||e.__experimentalNavigationBackButton,ve=e.NavigationGroup||e.__experimentalNavigationGroup,Ee=e.NavigationMenu||e.__experimentalNavigationMenu,ye=e.NavigationItem||e.__experimentalNavigationItem,xe=e.Text||e.__experimentalText,be=e.__experimentalUseSlotFills||(()=>null),we=t=>{const n=(e.useSlot||e.__experimentalUseSlot)(t),r=be(t);return"function"==typeof e.__experimentalUseSlotFills?{...n,fills:r}:n}})(),(window.wc=window.wc||{}).experimental=r})();
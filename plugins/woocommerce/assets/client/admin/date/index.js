(()=>{var e={40368:(e,t,r)=>{"use strict";var o=r(40885),n=r(11548),a=n(o("String.prototype.indexOf"));e.exports=function(e,t){var r=o(e,!!t);return"function"==typeof r&&a(e,".prototype.")>-1?n(r):r}},11548:(e,t,r)=>{"use strict";var o=r(72418),n=r(40885),a=r(73745),i=n("%TypeError%"),c=n("%Function.prototype.apply%"),l=n("%Function.prototype.call%"),u=n("%Reflect.apply%",!0)||o.call(l,c),p=n("%Object.defineProperty%",!0),f=n("%Math.max%");if(p)try{p({},"a",{value:1})}catch(e){p=null}e.exports=function(e){if("function"!=typeof e)throw new i("a function is required");var t=u(o,l,arguments);return a(t,1+f(0,e.length-(arguments.length-1)),!0)};var y=function(){return u(o,c,arguments)};p?p(e.exports,"apply",{value:y}):e.exports.apply=y},91555:(e,t,r)=>{"use strict";var o=r(87612)(),n=r(40885),a=o&&n("%Object.defineProperty%",!0);if(a)try{a({},"a",{value:1})}catch(e){a=!1}var i=n("%SyntaxError%"),c=n("%TypeError%"),l=r(8632);e.exports=function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new c("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new c("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new c("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new c("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new c("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new c("`loose`, if provided, must be a boolean");var o=arguments.length>3?arguments[3]:null,n=arguments.length>4?arguments[4]:null,u=arguments.length>5?arguments[5]:null,p=arguments.length>6&&arguments[6],f=!!l&&l(e,t);if(a)a(e,t,{configurable:null===u&&f?f.configurable:!u,enumerable:null===o&&f?f.enumerable:!o,value:r,writable:null===n&&f?f.writable:!n});else{if(!p&&(o||n||u))throw new i("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");e[t]=r}}},65786:e=>{"use strict";var t=Object.prototype.toString,r=Math.max,o=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r};e.exports=function(e){var n=this;if("function"!=typeof n||"[object Function]"!==t.apply(n))throw new TypeError("Function.prototype.bind called on incompatible "+n);for(var a,i=function(e){for(var t=[],r=1,o=0;r<e.length;r+=1,o+=1)t[o]=e[r];return t}(arguments),c=r(0,n.length-i.length),l=[],u=0;u<c;u++)l[u]="$"+u;if(a=Function("binder","return function ("+function(e){for(var t="",r=0;r<e.length;r+=1)t+=e[r],r+1<e.length&&(t+=",");return t}(l)+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof a){var t=n.apply(this,o(i,arguments));return Object(t)===t?t:this}return n.apply(e,o(i,arguments))})),n.prototype){var p=function(){};p.prototype=n.prototype,a.prototype=new p,p.prototype=null}return a}},72418:(e,t,r)=>{"use strict";var o=r(65786);e.exports=Function.prototype.bind||o},40885:(e,t,r)=>{"use strict";var o,n=SyntaxError,a=Function,i=TypeError,c=function(e){try{return a('"use strict"; return ('+e+").constructor;")()}catch(e){}},l=Object.getOwnPropertyDescriptor;if(l)try{l({},"")}catch(e){l=null}var u=function(){throw new i},p=l?function(){try{return u}catch(e){try{return l(arguments,"callee").get}catch(e){return u}}}():u,f=r(13518)(),y=r(64310)(),s=Object.getPrototypeOf||(y?function(e){return e.__proto__}:null),d={},m="undefined"!=typeof Uint8Array&&s?s(Uint8Array):o,b={"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":f&&s?s([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":d,"%AsyncGenerator%":d,"%AsyncGeneratorFunction%":d,"%AsyncIteratorPrototype%":d,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":a,"%GeneratorFunction%":d,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":f&&s?s(s([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&f&&s?s((new Map)[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&f&&s?s((new Set)[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":f&&s?s(""[Symbol.iterator]()):o,"%Symbol%":f?Symbol:o,"%SyntaxError%":n,"%ThrowTypeError%":p,"%TypedArray%":m,"%TypeError%":i,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet};if(s)try{null.error}catch(e){var g=s(s(e));b["%Error.prototype%"]=g}var h=function e(t){var r;if("%AsyncFunction%"===t)r=c("async function () {}");else if("%GeneratorFunction%"===t)r=c("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=c("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&s&&(r=s(n.prototype))}return b[t]=r,r},v={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},w=r(72418),S=r(63206),j=w.call(Function.call,Array.prototype.concat),A=w.call(Function.apply,Array.prototype.splice),O=w.call(Function.call,String.prototype.replace),_=w.call(Function.call,String.prototype.slice),P=w.call(Function.call,RegExp.prototype.exec),x=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,E=/\\(\\)?/g,k=function(e,t){var r,o=e;if(S(v,o)&&(o="%"+(r=v[o])[0]+"%"),S(b,o)){var a=b[o];if(a===d&&(a=h(o)),void 0===a&&!t)throw new i("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:a}}throw new n("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new i("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new i('"allowMissing" argument must be a boolean');if(null===P(/^%?[^%]*%?$/,e))throw new n("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=_(e,0,1),r=_(e,-1);if("%"===t&&"%"!==r)throw new n("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new n("invalid intrinsic syntax, expected opening `%`");var o=[];return O(e,x,(function(e,t,r,n){o[o.length]=r?O(n,E,"$1"):t||e})),o}(e),o=r.length>0?r[0]:"",a=k("%"+o+"%",t),c=a.name,u=a.value,p=!1,f=a.alias;f&&(o=f[0],A(r,j([0,1],f)));for(var y=1,s=!0;y<r.length;y+=1){var d=r[y],m=_(d,0,1),g=_(d,-1);if(('"'===m||"'"===m||"`"===m||'"'===g||"'"===g||"`"===g)&&m!==g)throw new n("property names with quotes must have matching quotes");if("constructor"!==d&&s||(p=!0),S(b,c="%"+(o+="."+d)+"%"))u=b[c];else if(null!=u){if(!(d in u)){if(!t)throw new i("base intrinsic for "+e+" exists, but the property is not available.");return}if(l&&y+1>=r.length){var h=l(u,d);u=(s=!!h)&&"get"in h&&!("originalValue"in h.get)?h.get:u[d]}else s=S(u,d),u=u[d];s&&!p&&(b[c]=u)}}return u}},8632:(e,t,r)=>{"use strict";var o=r(40885)("%Object.getOwnPropertyDescriptor%",!0);if(o)try{o([],"length")}catch(e){o=null}e.exports=o},87612:(e,t,r)=>{"use strict";var o=r(40885)("%Object.defineProperty%",!0),n=function(){if(o)try{return o({},"a",{value:1}),!0}catch(e){return!1}return!1};n.hasArrayLengthDefineBug=function(){if(!n())return null;try{return 1!==o([],"length",{value:1}).length}catch(e){return!0}},e.exports=n},64310:e=>{"use strict";var t={foo:{}},r=Object;e.exports=function(){return{__proto__:t}.foo===t.foo&&!({__proto__:null}instanceof r)}},13518:(e,t,r)=>{"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=r(71108);e.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},71108:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var o=Object.getOwnPropertySymbols(e);if(1!==o.length||o[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var n=Object.getOwnPropertyDescriptor(e,t);if(42!==n.value||!0!==n.enumerable)return!1}return!0}},63206:(e,t,r)=>{"use strict";var o=Function.prototype.call,n=Object.prototype.hasOwnProperty,a=r(72418);e.exports=a.call(o,n)},83282:(e,t,r)=>{var o="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,a=o&&n&&"function"==typeof n.get?n.get:null,i=o&&Map.prototype.forEach,c="function"==typeof Set&&Set.prototype,l=Object.getOwnPropertyDescriptor&&c?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=c&&l&&"function"==typeof l.get?l.get:null,p=c&&Set.prototype.forEach,f="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,y="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,s="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,d=Boolean.prototype.valueOf,m=Object.prototype.toString,b=Function.prototype.toString,g=String.prototype.match,h=String.prototype.slice,v=String.prototype.replace,w=String.prototype.toUpperCase,S=String.prototype.toLowerCase,j=RegExp.prototype.test,A=Array.prototype.concat,O=Array.prototype.join,_=Array.prototype.slice,P=Math.floor,x="function"==typeof BigInt?BigInt.prototype.valueOf:null,E=Object.getOwnPropertySymbols,k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,F="function"==typeof Symbol&&"object"==typeof Symbol.iterator,M="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol.toStringTag,1)?Symbol.toStringTag:null,Y=Object.prototype.propertyIsEnumerable,D=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function I(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||j.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-P(-e):P(e);if(o!==e){var n=String(o),a=h.call(t,n.length+1);return v.call(n,r,"$&_")+"."+v.call(v.call(a,/([0-9]{3})/g,"$&_"),/_$/,"")}}return v.call(t,r,"$&_")}var R=r(70123),T=R.custom,N=W(T)?T:null;function L(e,t,r){var o="double"===(r.quoteStyle||t)?'"':"'";return o+e+o}function B(e){return v.call(String(e),/"/g,"&quot;")}function U(e){return!("[object Array]"!==H(e)||M&&"object"==typeof e&&M in e)}function C(e){return!("[object RegExp]"!==H(e)||M&&"object"==typeof e&&M in e)}function W(e){if(F)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!k)return!1;try{return k.call(e),!0}catch(e){}return!1}e.exports=function e(t,o,n,c){var l=o||{};if($(l,"quoteStyle")&&"single"!==l.quoteStyle&&"double"!==l.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if($(l,"maxStringLength")&&("number"==typeof l.maxStringLength?l.maxStringLength<0&&l.maxStringLength!==1/0:null!==l.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var m=!$(l,"customInspect")||l.customInspect;if("boolean"!=typeof m&&"symbol"!==m)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if($(l,"indent")&&null!==l.indent&&"\t"!==l.indent&&!(parseInt(l.indent,10)===l.indent&&l.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if($(l,"numericSeparator")&&"boolean"!=typeof l.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var w=l.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return G(t,l);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var j=String(t);return w?I(t,j):j}if("bigint"==typeof t){var P=String(t)+"n";return w?I(t,P):P}var E=void 0===l.depth?5:l.depth;if(void 0===n&&(n=0),n>=E&&E>0&&"object"==typeof t)return U(t)?"[Array]":"[Object]";var T,q=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=O.call(Array(e.indent+1)," ")}return{base:r,prev:O.call(Array(t+1),r)}}(l,n);if(void 0===c)c=[];else if(V(c,t)>=0)return"[Circular]";function z(t,r,o){if(r&&(c=_.call(c)).push(r),o){var a={depth:l.depth};return $(l,"quoteStyle")&&(a.quoteStyle=l.quoteStyle),e(t,a,n+1,c)}return e(t,l,n+1,c)}if("function"==typeof t&&!C(t)){var ee=function(e){if(e.name)return e.name;var t=g.call(b.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),te=X(t,z);return"[Function"+(ee?": "+ee:" (anonymous)")+"]"+(te.length>0?" { "+O.call(te,", ")+" }":"")}if(W(t)){var re=F?v.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):k.call(t);return"object"!=typeof t||F?re:Q(re)}if((T=t)&&"object"==typeof T&&("undefined"!=typeof HTMLElement&&T instanceof HTMLElement||"string"==typeof T.nodeName&&"function"==typeof T.getAttribute)){for(var oe="<"+S.call(String(t.nodeName)),ne=t.attributes||[],ae=0;ae<ne.length;ae++)oe+=" "+ne[ae].name+"="+L(B(ne[ae].value),"double",l);return oe+=">",t.childNodes&&t.childNodes.length&&(oe+="..."),oe+"</"+S.call(String(t.nodeName))+">"}if(U(t)){if(0===t.length)return"[]";var ie=X(t,z);return q&&!function(e){for(var t=0;t<e.length;t++)if(V(e[t],"\n")>=0)return!1;return!0}(ie)?"["+K(ie,q)+"]":"[ "+O.call(ie,", ")+" ]"}if(function(e){return!("[object Error]"!==H(e)||M&&"object"==typeof e&&M in e)}(t)){var ce=X(t,z);return"cause"in Error.prototype||!("cause"in t)||Y.call(t,"cause")?0===ce.length?"["+String(t)+"]":"{ ["+String(t)+"] "+O.call(ce,", ")+" }":"{ ["+String(t)+"] "+O.call(A.call("[cause]: "+z(t.cause),ce),", ")+" }"}if("object"==typeof t&&m){if(N&&"function"==typeof t[N]&&R)return R(t,{depth:E-n});if("symbol"!==m&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!a||!e||"object"!=typeof e)return!1;try{a.call(e);try{u.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var le=[];return i&&i.call(t,(function(e,r){le.push(z(r,t,!0)+" => "+z(e,t))})),Z("Map",a.call(t),le,q)}if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e);try{a.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var ue=[];return p&&p.call(t,(function(e){ue.push(z(e,t))})),Z("Set",u.call(t),ue,q)}if(function(e){if(!f||!e||"object"!=typeof e)return!1;try{f.call(e,f);try{y.call(e,y)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return J("WeakMap");if(function(e){if(!y||!e||"object"!=typeof e)return!1;try{y.call(e,y);try{f.call(e,f)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return J("WeakSet");if(function(e){if(!s||!e||"object"!=typeof e)return!1;try{return s.call(e),!0}catch(e){}return!1}(t))return J("WeakRef");if(function(e){return!("[object Number]"!==H(e)||M&&"object"==typeof e&&M in e)}(t))return Q(z(Number(t)));if(function(e){if(!e||"object"!=typeof e||!x)return!1;try{return x.call(e),!0}catch(e){}return!1}(t))return Q(z(x.call(t)));if(function(e){return!("[object Boolean]"!==H(e)||M&&"object"==typeof e&&M in e)}(t))return Q(d.call(t));if(function(e){return!("[object String]"!==H(e)||M&&"object"==typeof e&&M in e)}(t))return Q(z(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if(t===r.g)return"{ [object globalThis] }";if(!function(e){return!("[object Date]"!==H(e)||M&&"object"==typeof e&&M in e)}(t)&&!C(t)){var pe=X(t,z),fe=D?D(t)===Object.prototype:t instanceof Object||t.constructor===Object,ye=t instanceof Object?"":"null prototype",se=!fe&&M&&Object(t)===t&&M in t?h.call(H(t),8,-1):ye?"Object":"",de=(fe||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(se||ye?"["+O.call(A.call([],se||[],ye||[]),": ")+"] ":"");return 0===pe.length?de+"{}":q?de+"{"+K(pe,q)+"}":de+"{ "+O.call(pe,", ")+" }"}return String(t)};var q=Object.prototype.hasOwnProperty||function(e){return e in this};function $(e,t){return q.call(e,t)}function H(e){return m.call(e)}function V(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return-1}function G(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,o="... "+r+" more character"+(r>1?"s":"");return G(h.call(e,0,t.maxStringLength),t)+o}return L(v.call(v.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,z),"single",t)}function z(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+w.call(t.toString(16))}function Q(e){return"Object("+e+")"}function J(e){return e+" { ? }"}function Z(e,t,r,o){return e+" ("+t+") {"+(o?K(r,o):O.call(r,", "))+"}"}function K(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+O.call(e,","+r)+"\n"+t.prev}function X(e,t){var r=U(e),o=[];if(r){o.length=e.length;for(var n=0;n<e.length;n++)o[n]=$(e,n)?t(e[n],e):""}var a,i="function"==typeof E?E(e):[];if(F){a={};for(var c=0;c<i.length;c++)a["$"+i[c]]=i[c]}for(var l in e)$(e,l)&&(r&&String(Number(l))===l&&l<e.length||F&&a["$"+l]instanceof Symbol||(j.call(/[^\w$]/,l)?o.push(t(l,e)+": "+t(e[l],e)):o.push(l+": "+t(e[l],e))));if("function"==typeof E)for(var u=0;u<i.length;u++)Y.call(e,i[u])&&o.push("["+t(i[u])+"]: "+t(e[i[u]],e));return o}},24294:e=>{"use strict";var t=String.prototype.replace,r=/%20/g,o="RFC3986";e.exports={default:o,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:o}},4594:(e,t,r)=>{"use strict";var o=r(61007),n=r(84977),a=r(24294);e.exports={formats:a,parse:n,stringify:o}},84977:(e,t,r)=>{"use strict";var o=r(50323),n=Object.prototype.hasOwnProperty,a=Array.isArray,i={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:o.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},c=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},l=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},u=function(e,t,r,o){if(e){var a=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,i=/(\[[^[\]]*])/g,c=r.depth>0&&/(\[[^[\]]*])/.exec(a),u=c?a.slice(0,c.index):a,p=[];if(u){if(!r.plainObjects&&n.call(Object.prototype,u)&&!r.allowPrototypes)return;p.push(u)}for(var f=0;r.depth>0&&null!==(c=i.exec(a))&&f<r.depth;){if(f+=1,!r.plainObjects&&n.call(Object.prototype,c[1].slice(1,-1))&&!r.allowPrototypes)return;p.push(c[1])}return c&&p.push("["+a.slice(c.index)+"]"),function(e,t,r,o){for(var n=o?t:l(t,r),a=e.length-1;a>=0;--a){var i,c=e[a];if("[]"===c&&r.parseArrays)i=[].concat(n);else{i=r.plainObjects?Object.create(null):{};var u="["===c.charAt(0)&&"]"===c.charAt(c.length-1)?c.slice(1,-1):c,p=parseInt(u,10);r.parseArrays||""!==u?!isNaN(p)&&c!==u&&String(p)===u&&p>=0&&r.parseArrays&&p<=r.arrayLimit?(i=[])[p]=n:"__proto__"!==u&&(i[u]=n):i={0:n}}n=i}return n}(p,t,r,o)}};e.exports=function(e,t){var r=function(e){if(!e)return i;if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?i.charset:e.charset;return{allowDots:void 0===e.allowDots?i.allowDots:!!e.allowDots,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:i.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:i.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:i.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:i.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:i.comma,decoder:"function"==typeof e.decoder?e.decoder:i.decoder,delimiter:"string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e.delimiter:i.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:i.depth,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:i.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:i.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:i.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:i.strictNullHandling}}(t);if(""===e||null==e)return r.plainObjects?Object.create(null):{};for(var p="string"==typeof e?function(e,t){var r,u={__proto__:null},p=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,f=t.parameterLimit===1/0?void 0:t.parameterLimit,y=p.split(t.delimiter,f),s=-1,d=t.charset;if(t.charsetSentinel)for(r=0;r<y.length;++r)0===y[r].indexOf("utf8=")&&("utf8=%E2%9C%93"===y[r]?d="utf-8":"utf8=%26%2310003%3B"===y[r]&&(d="iso-8859-1"),s=r,r=y.length);for(r=0;r<y.length;++r)if(r!==s){var m,b,g=y[r],h=g.indexOf("]="),v=-1===h?g.indexOf("="):h+1;-1===v?(m=t.decoder(g,i.decoder,d,"key"),b=t.strictNullHandling?null:""):(m=t.decoder(g.slice(0,v),i.decoder,d,"key"),b=o.maybeMap(l(g.slice(v+1),t),(function(e){return t.decoder(e,i.decoder,d,"value")}))),b&&t.interpretNumericEntities&&"iso-8859-1"===d&&(b=c(b)),g.indexOf("[]=")>-1&&(b=a(b)?[b]:b),n.call(u,m)?u[m]=o.combine(u[m],b):u[m]=b}return u}(e,r):e,f=r.plainObjects?Object.create(null):{},y=Object.keys(p),s=0;s<y.length;++s){var d=y[s],m=u(d,p[d],r,"string"==typeof e);f=o.merge(f,m,r)}return!0===r.allowSparse?f:o.compact(f)}},61007:(e,t,r)=>{"use strict";var o=r(2435),n=r(50323),a=r(24294),i=Object.prototype.hasOwnProperty,c={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},l=Array.isArray,u=Array.prototype.push,p=function(e,t){u.apply(e,l(t)?t:[t])},f=Date.prototype.toISOString,y=a.default,s={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:n.encode,encodeValuesOnly:!1,format:y,formatter:a.formatters[y],indices:!1,serializeDate:function(e){return f.call(e)},skipNulls:!1,strictNullHandling:!1},d={},m=function e(t,r,a,i,c,u,f,y,m,b,g,h,v,w,S,j){for(var A,O=t,_=j,P=0,x=!1;void 0!==(_=_.get(d))&&!x;){var E=_.get(t);if(P+=1,void 0!==E){if(E===P)throw new RangeError("Cyclic object value");x=!0}void 0===_.get(d)&&(P=0)}if("function"==typeof y?O=y(r,O):O instanceof Date?O=g(O):"comma"===a&&l(O)&&(O=n.maybeMap(O,(function(e){return e instanceof Date?g(e):e}))),null===O){if(c)return f&&!w?f(r,s.encoder,S,"key",h):r;O=""}if("string"==typeof(A=O)||"number"==typeof A||"boolean"==typeof A||"symbol"==typeof A||"bigint"==typeof A||n.isBuffer(O))return f?[v(w?r:f(r,s.encoder,S,"key",h))+"="+v(f(O,s.encoder,S,"value",h))]:[v(r)+"="+v(String(O))];var k,F=[];if(void 0===O)return F;if("comma"===a&&l(O))w&&f&&(O=n.maybeMap(O,f)),k=[{value:O.length>0?O.join(",")||null:void 0}];else if(l(y))k=y;else{var M=Object.keys(O);k=m?M.sort(m):M}for(var Y=i&&l(O)&&1===O.length?r+"[]":r,D=0;D<k.length;++D){var I=k[D],R="object"==typeof I&&void 0!==I.value?I.value:O[I];if(!u||null!==R){var T=l(O)?"function"==typeof a?a(Y,I):Y:Y+(b?"."+I:"["+I+"]");j.set(t,P);var N=o();N.set(d,j),p(F,e(R,T,a,i,c,u,"comma"===a&&w&&l(O)?null:f,y,m,b,g,h,v,w,S,N))}}return F};e.exports=function(e,t){var r,n=e,u=function(e){if(!e)return s;if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||s.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=a.default;if(void 0!==e.format){if(!i.call(a.formatters,e.format))throw new TypeError("Unknown format option provided.");r=e.format}var o=a.formatters[r],n=s.filter;return("function"==typeof e.filter||l(e.filter))&&(n=e.filter),{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:s.addQueryPrefix,allowDots:void 0===e.allowDots?s.allowDots:!!e.allowDots,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:s.charsetSentinel,delimiter:void 0===e.delimiter?s.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:s.encode,encoder:"function"==typeof e.encoder?e.encoder:s.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:s.encodeValuesOnly,filter:n,format:r,formatter:o,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:s.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:s.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:s.strictNullHandling}}(t);"function"==typeof u.filter?n=(0,u.filter)("",n):l(u.filter)&&(r=u.filter);var f,y=[];if("object"!=typeof n||null===n)return"";f=t&&t.arrayFormat in c?t.arrayFormat:t&&"indices"in t?t.indices?"indices":"repeat":"indices";var d=c[f];if(t&&"commaRoundTrip"in t&&"boolean"!=typeof t.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var b="comma"===d&&t&&t.commaRoundTrip;r||(r=Object.keys(n)),u.sort&&r.sort(u.sort);for(var g=o(),h=0;h<r.length;++h){var v=r[h];u.skipNulls&&null===n[v]||p(y,m(n[v],v,d,b,u.strictNullHandling,u.skipNulls,u.encode?u.encoder:null,u.filter,u.sort,u.allowDots,u.serializeDate,u.format,u.formatter,u.encodeValuesOnly,u.charset,g))}var w=y.join(u.delimiter),S=!0===u.addQueryPrefix?"?":"";return u.charsetSentinel&&("iso-8859-1"===u.charset?S+="utf8=%26%2310003%3B&":S+="utf8=%E2%9C%93&"),w.length>0?S+w:""}},50323:(e,t,r)=>{"use strict";var o=r(24294),n=Object.prototype.hasOwnProperty,a=Array.isArray,i=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),c=function(e,t){for(var r=t&&t.plainObjects?Object.create(null):{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r};e.exports={arrayToObject:c,assign:function(e,t){return Object.keys(t).reduce((function(e,r){return e[r]=t[r],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],o=0;o<t.length;++o)for(var n=t[o],i=n.obj[n.prop],c=Object.keys(i),l=0;l<c.length;++l){var u=c[l],p=i[u];"object"==typeof p&&null!==p&&-1===r.indexOf(p)&&(t.push({obj:i,prop:u}),r.push(p))}return function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(a(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}}(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},encode:function(e,t,r,n,a){if(0===e.length)return e;var c=e;if("symbol"==typeof e?c=Symbol.prototype.toString.call(e):"string"!=typeof e&&(c=String(e)),"iso-8859-1"===r)return escape(c).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var l="",u=0;u<c.length;++u){var p=c.charCodeAt(u);45===p||46===p||95===p||126===p||p>=48&&p<=57||p>=65&&p<=90||p>=97&&p<=122||a===o.RFC1738&&(40===p||41===p)?l+=c.charAt(u):p<128?l+=i[p]:p<2048?l+=i[192|p>>6]+i[128|63&p]:p<55296||p>=57344?l+=i[224|p>>12]+i[128|p>>6&63]+i[128|63&p]:(u+=1,p=65536+((1023&p)<<10|1023&c.charCodeAt(u)),l+=i[240|p>>18]+i[128|p>>12&63]+i[128|p>>6&63]+i[128|63&p])}return l},isBuffer:function(e){return!(!e||"object"!=typeof e||!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e)))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(a(e)){for(var r=[],o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r){if(a(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!n.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var i=t;return a(t)&&!a(r)&&(i=c(t,o)),a(t)&&a(r)?(r.forEach((function(r,a){if(n.call(t,a)){var i=t[a];i&&"object"==typeof i&&r&&"object"==typeof r?t[a]=e(i,r,o):t.push(r)}else t[a]=r})),t):Object.keys(r).reduce((function(t,a){var i=r[a];return n.call(t,a)?t[a]=e(t[a],i,o):t[a]=i,t}),i)}}},73745:(e,t,r)=>{"use strict";var o=r(40885),n=r(91555),a=r(87612)(),i=r(8632),c=o("%TypeError%"),l=o("%Math.floor%");e.exports=function(e,t){if("function"!=typeof e)throw new c("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||l(t)!==t)throw new c("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],o=!0,u=!0;if("length"in e&&i){var p=i(e,"length");p&&!p.configurable&&(o=!1),p&&!p.writable&&(u=!1)}return(o||u||!r)&&(a?n(e,"length",t,!0,!0):n(e,"length",t)),e}},2435:(e,t,r)=>{"use strict";var o=r(40885),n=r(40368),a=r(83282),i=o("%TypeError%"),c=o("%WeakMap%",!0),l=o("%Map%",!0),u=n("WeakMap.prototype.get",!0),p=n("WeakMap.prototype.set",!0),f=n("WeakMap.prototype.has",!0),y=n("Map.prototype.get",!0),s=n("Map.prototype.set",!0),d=n("Map.prototype.has",!0),m=function(e,t){for(var r,o=e;null!==(r=o.next);o=r)if(r.key===t)return o.next=r.next,r.next=e.next,e.next=r,r};e.exports=function(){var e,t,r,o={assert:function(e){if(!o.has(e))throw new i("Side channel does not contain "+a(e))},get:function(o){if(c&&o&&("object"==typeof o||"function"==typeof o)){if(e)return u(e,o)}else if(l){if(t)return y(t,o)}else if(r)return function(e,t){var r=m(e,t);return r&&r.value}(r,o)},has:function(o){if(c&&o&&("object"==typeof o||"function"==typeof o)){if(e)return f(e,o)}else if(l){if(t)return d(t,o)}else if(r)return function(e,t){return!!m(e,t)}(r,o);return!1},set:function(o,n){c&&o&&("object"==typeof o||"function"==typeof o)?(e||(e=new c),p(e,o,n)):l?(t||(t=new l),s(t,o,n)):(r||(r={key:{},next:null}),function(e,t,r){var o=m(e,t);o?o.value=r:e.next={key:t,next:e.next,value:r}}(r,o,n))}};return o}},70123:()=>{}},t={};function r(o){var n=t[o];if(void 0!==n)return n.exports;var a=t[o]={exports:{}};return e[o](a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};(()=>{"use strict";r.r(o),r.d(o,{appendTimestamp:()=>y,containsLeapYear:()=>L,dateValidationMessages:()=>R,dayTicksThreshold:()=>E,defaultDateTimeFormat:()=>l,defaultTableDateFormat:()=>F,getAllowedIntervalsForQuery:()=>_,getChartTypeForQuery:()=>x,getCurrentDates:()=>j,getCurrentPeriod:()=>g,getDateDifferenceInDays:()=>A,getDateFormatsForInterval:()=>D,getDateFormatsForIntervalD3:()=>M,getDateFormatsForIntervalPhp:()=>Y,getDateParamsFromQuery:()=>w,getIntervalForQuery:()=>P,getLastPeriod:()=>b,getPreviousDate:()=>O,getRangeLabel:()=>d,getStoreTimeZoneMoment:()=>m,isLeapYear:()=>N,isoDateFormat:()=>c,loadLocaleData:()=>I,periods:()=>p,presetValues:()=>u,toMoment:()=>s,validateDateInputForRange:()=>T,weekTicksThreshold:()=>k});const e=window.moment;var t=r.n(e);const n=window.lodash,a=window.wp.i18n;var i=r(4594);const c="YYYY-MM-DD",l="YYYY-MM-DDTHH:mm:ss",u=[{value:"today",label:(0,a.__)("Today","woocommerce")},{value:"yesterday",label:(0,a.__)("Yesterday","woocommerce")},{value:"week",label:(0,a.__)("Week to date","woocommerce")},{value:"last_week",label:(0,a.__)("Last week","woocommerce")},{value:"month",label:(0,a.__)("Month to date","woocommerce")},{value:"last_month",label:(0,a.__)("Last month","woocommerce")},{value:"quarter",label:(0,a.__)("Quarter to date","woocommerce")},{value:"last_quarter",label:(0,a.__)("Last quarter","woocommerce")},{value:"year",label:(0,a.__)("Year to date","woocommerce")},{value:"last_year",label:(0,a.__)("Last year","woocommerce")},{value:"custom",label:(0,a.__)("Custom","woocommerce")}],p=[{value:"previous_period",label:(0,a.__)("Previous period","woocommerce")},{value:"previous_year",label:(0,a.__)("Previous year","woocommerce")}],f=e=>t()(e).isValid(),y=(e,t)=>{if("start"===t)return e.startOf("day").format(l);if("now"===t)return e.format(l);if("end"===t)return e.endOf("day").format(l);throw new Error("appendTimestamp requires second parameter to be either `start`, `now` or `end`")};function s(e,r){if(t().isMoment(r))return r.isValid()?r:null;if("string"==typeof r){const o=t()(r,[c,e],!0);return o.isValid()?o:null}throw new Error("toMoment requires a string to be passed as an argument")}function d(e,t){const r=e.year()===t.year(),o=r&&e.month()===t.month(),n=r&&o&&e.isSame(t,"day"),i=(0,a.__)("MMM D, YYYY","woocommerce");if(n)return e.format(i);if(o){const r=e.date();return e.format(i).replace(String(r),`${r} - ${t.date()}`)}if(r){const r=(0,a.__)("MMM D","woocommerce");return`${e.format(r)} - ${t.format(i)}`}return`${e.format(i)} - ${t.format(i)}`}function m(){return window.wcSettings&&window.wcSettings.timeZone?["+","-"].includes(window.wcSettings.timeZone.charAt(0))?t()().utcOffset(window.wcSettings.timeZone):t()().tz(window.wcSettings.timeZone):t()()}function b(e,r){const o=m().startOf(e).subtract(1,e),n=o.clone().endOf(e);let a,i;if("previous_period"===r)if("year"===e)a=t()().startOf(e).subtract(2,e),i=a.clone().endOf(e);else{const e=n.diff(o,"days");i=o.clone().subtract(1,"days"),a=i.clone().subtract(e,"days")}else"week"===e?(a=o.clone().subtract(1,"years"),i=n.clone().subtract(1,"years")):(a=o.clone().subtract(1,"years"),i=a.clone().endOf(e));return"month"===e&&(i=i.clone().endOf("month")),{primaryStart:o,primaryEnd:n,secondaryStart:a,secondaryEnd:i}}function g(e,t){const r=m().startOf(e),o=m(),n=o.diff(r,"days");let a,i;return"previous_period"===t?(a=r.clone().subtract(1,e),i=o.clone().subtract(1,e)):(a=r.clone().subtract(1,"years"),i=a.clone().add(n+1,"days").subtract(1,"seconds")),{primaryStart:r,primaryEnd:o,secondaryStart:a,secondaryEnd:i}}const h=(0,n.memoize)(((e,t,r,o)=>{switch(e){case"today":return g("day",t);case"yesterday":return b("day",t);case"week":return g("week",t);case"last_week":return b("week",t);case"month":return g("month",t);case"last_month":return b("month",t);case"quarter":return g("quarter",t);case"last_quarter":return b("quarter",t);case"year":return g("year",t);case"last_year":return b("year",t);case"custom":if(!r||!o)throw Error("Custom date range requires both after and before dates.");const e=o.diff(r,"days");if("previous_period"===t){const t=r.clone().subtract(1,"days");return{primaryStart:r,primaryEnd:o,secondaryStart:t.clone().subtract(e,"days"),secondaryEnd:t}}return{primaryStart:r,primaryEnd:o,secondaryStart:r.clone().subtract(1,"years"),secondaryEnd:o.clone().subtract(1,"years")}}}),((e,t,r,o)=>[e,t,r&&r.format(),o&&o.format()].join(":"))),v=(0,n.memoize)(((e,r,o,n,a)=>{if(e&&r)return{period:e,compare:r,after:o?t()(o):null,before:n?t()(n):null};const c=(0,i.parse)(a.replace(/&amp;/g,"&"));return"string"!=typeof c.period&&(console.warn(`Unexpected default period type ${c.period}`),c.period=""),"string"!=typeof c.compare&&(console.warn(`Unexpected default compare type ${c.compare}`),c.compare=""),{period:c.period,compare:c.compare,after:c.after&&f(c.after)?t()(c.after):null,before:c.before&&f(c.before)?t()(c.before):null}}),((e,t,r,o,n)=>[e,t,r,o,n].join(":"))),w=(e,t="period=month&compare=previous_year")=>{const{period:r,compare:o,after:n,before:a}=e;return v(r,o,n,a,t)},S=(0,n.memoize)(((e,t,r,o,a,i)=>{const c=(0,n.find)(u,(t=>t.value===e));if(!c)throw new Error(`Cannot find period: ${e}`);const l=(0,n.find)(p,(e=>e.value===t));if(!l)throw new Error(`Cannot find compare: ${t}`);return{primary:{label:c.label,range:d(r,o),after:r,before:o},secondary:{label:l.label,range:d(a,i),after:a,before:i}}}),((e,t,r,o,n,a)=>[e,t,r&&r.format(),o&&o.format(),n&&n.format(),a&&a.format()].join(":"))),j=(e,t="period=month&compare=previous_year")=>{const{period:r,compare:o,after:n,before:a}=w(e,t),i=h(r,o,n,a);if(!i)throw Error("Invalid date range");const{primaryStart:c,primaryEnd:l,secondaryStart:u,secondaryEnd:p}=i;return S(r,o,c,l,u,p)},A=(e,r)=>{const o=t()(e),n=t()(r);return o.diff(n,"days")},O=(e,r,o,n="previous_year",a)=>{const i=t()(e);if("previous_year"===n)return i.clone().subtract(1,"years");const c=t()(r),l=t()(o),u=c.diff(l,a);return i.clone().subtract(u,a)};function _(e,t="period=&compare=previous_year"){const{period:r}=w(e,t);let o=[];if("custom"===r){const{primary:t}=j(e),r=A(t.before,t.after);o=r>=365?["day","week","month","quarter","year"]:r>=90?["day","week","month","quarter"]:r>=28?["day","week","month"]:r>=7?["day","week"]:r>1&&r<7?["day"]:["hour","day"]}else switch(r){case"today":case"yesterday":o=["hour","day"];break;case"week":case"last_week":default:o=["day"];break;case"month":case"last_month":o=["day","week"];break;case"quarter":case"last_quarter":o=["day","week","month"];break;case"year":case"last_year":o=["day","week","month","quarter"]}return o}function P(e,t="period=&compare=previous_year"){const r=_(e,t),o=r[0];let n=e.interval||o;return e.interval&&!r.includes(e.interval)&&(n=o),n}function x({chartType:e}){return void 0!==e&&["line","bar"].includes(e)?e:"line"}const E=63,k=9,F="m/d/Y";function M(e,t=0){let r="%B %-d, %Y",o="%B %-d, %Y",n="%Y-%m-%d",i="%b %Y",c=F;switch(e){case"hour":r="%_I%p %B %-d, %Y",o="%_I%p %b %-d, %Y",n="%_I%p",i="%b %-d, %Y",c="h A";break;case"day":t<E?n="%-d":(n="%b",i="%Y");break;case"week":t<k?(n="%-d",i="%b %Y"):(n="%b",i="%Y"),r=(0,a.__)("Week of %B %-d, %Y","woocommerce"),o=(0,a.__)("Week of %B %-d, %Y","woocommerce");break;case"quarter":case"month":r="%B %Y",o="%B %Y",n="%b",i="%Y";break;case"year":r="%Y",o="%Y",n="%Y"}return{screenReaderFormat:r,tooltipLabelFormat:o,xFormat:n,x2Format:i,tableFormat:c}}function Y(e,t=0){let r="F j, Y",o="F j, Y",n="Y-m-d",i="M Y",c=F;switch(e){case"hour":r="gA F j, Y",o="gA M j, Y",n="gA",i="M j, Y",c="h A";break;case"day":t<E?n="j":(n="M",i="Y");break;case"week":t<k?(n="j",i="M Y"):(n="M",i="Y");const e=(0,a.__)("Week of","woocommerce").replace(/(\w)/g,"\\$1");r=`${e} F j, Y`,o=`${e} F j, Y`;break;case"quarter":case"month":r="F Y",o="F Y",n="M",i="Y";break;case"year":r="Y",o="Y",n="Y"}return{screenReaderFormat:r,tooltipLabelFormat:o,xFormat:n,x2Format:i,tableFormat:c}}function D(e,t=0,r={type:"d3"}){return"php"===r.type?Y(e,t):M(e,t)}function I({userLocale:e,weekdaysShort:r}){"en"!==t().locale()&&t().updateLocale(e,{longDateFormat:{L:(0,a.__)("MM/DD/YYYY","woocommerce"),LL:(0,a.__)("MMMM D, YYYY","woocommerce"),LLL:(0,a.__)("D MMMM YYYY LT","woocommerce"),LLLL:(0,a.__)("dddd, D MMMM YYYY LT","woocommerce"),LT:(0,a.__)("HH:mm","woocommerce"),LTS:"h:mm:ss A"},weekdaysMin:r})}const R={invalid:(0,a.__)("Invalid date","woocommerce"),future:(0,a.__)("Select a date in the past","woocommerce"),startAfterEnd:(0,a.__)("Start date must be before end date","woocommerce"),endBeforeStart:(0,a.__)("Start date must be before end date","woocommerce")};function T(e,r,o,n,a){const i=s(a,r);return i?t()().isBefore(i,"day")?{date:null,error:R.future}:"after"===e&&o&&i.isAfter(o,"day")?{date:null,error:R.startAfterEnd}:"before"===e&&n&&i.isBefore(n,"day")?{date:null,error:R.endBeforeStart}:{date:i}:{date:null,error:R.invalid}}function N(e){return e%4==0&&e%100!=0||e%400==0}function L(e,t){const r=new Date(e).getFullYear(),o=new Date(t).getFullYear();if(!isNaN(r)&&!isNaN(o))for(let e=r;e<=o;e++)if(N(e))return!0;return!1}})(),(window.wc=window.wc||{}).date=o})();
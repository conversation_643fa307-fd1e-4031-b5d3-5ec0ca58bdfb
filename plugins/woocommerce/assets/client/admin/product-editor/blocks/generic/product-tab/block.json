{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-tab", "title": "Product tab", "category": "woocommerce", "description": "The product tab.", "keywords": ["products", "tab", "group"], "textdomain": "default", "attributes": {"id": {"type": "string"}, "title": {"type": "string"}}, "supports": {"align": false, "html": false, "multiple": true, "reusable": false, "inserter": false, "lock": false, "__experimentalToolbar": false}, "providesContext": {"isInSelectedTab": "isSelected"}, "usesContext": ["selectedTab"], "editorStyle": "file:./editor.css"}
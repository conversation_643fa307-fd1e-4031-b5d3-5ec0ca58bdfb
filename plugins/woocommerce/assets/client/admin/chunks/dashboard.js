"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[945],{40125:(e,s,t)=>{t.r(s),t.d(s,{default:()=>d});var o=t(86087),r=t(27723),n=t(98846),a=t(76861),i=t(39793);const c=(0,o.lazy)((()=>t.e(3404).then(t.bind(t,53847))));class u extends o.Component{render(){const{path:e,query:s}=this.props;return(0,i.jsxs)(o.Suspense,{fallback:(0,i.jsx)(n.<PERSON>,{}),children:[(0,i.jsx)(a.Tv,{title:(0,r.__)("Discover what drives your sales","woocommerce"),description:(0,r.__)("Understand what truly drives revenue with our powerful order attribution extension. Use it to track your sales journey, identify your most effective marketing channels, and optimize your sales strategy.","woocommerce"),buttonText:(0,r.__)("Try it now","woocommerce"),badgeText:(0,r.__)("New","woocommerce"),bannerImage:(0,i.jsx)(a.iR,{}),dismissable:!0}),(0,i.jsx)(c,{query:s,path:e})]})}}const d=u}}]);
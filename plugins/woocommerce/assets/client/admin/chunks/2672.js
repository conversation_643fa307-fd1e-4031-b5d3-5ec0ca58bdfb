/*! For license information please see 2672.js.LICENSE.txt */
(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[2672],{33396:(e,t,r)=>{"use strict";r.d(t,{A:()=>v});var n=r(47143),s=r(86087),i=r(3582),a=r(87214),o=r(70351),c=r(27723),l=r(18537);const u=(e,t)=>`<a ${d(e)}>${t}</a>`,d=e=>`href="${e}" target="_blank" rel="noreferrer noopener"`,p=e=>{const{title:t,foreign_landing_url:r,creator:n,creator_url:s,license:i,license_version:a,license_url:o}=e,p=((e,t)=>{let r=e.trim();return"pdm"!==e&&(r=e.toUpperCase().replace("SAMPLING","Sampling")),t&&(r+=` ${t}`),["pdm","cc0"].includes(e)||(r=`CC ${r}`),r})(i,a),f=(0,l.decodeEntities)(n);let g;return g=f?t?(0,c.sprintf)((0,c._x)('"%1$s" by %2$s/ %3$s',"caption"),u(r,(0,l.decodeEntities)(t)),s?u(s,f):f,o?u(`${o}?ref=openverse`,p):p):(0,c.sprintf)((0,c._x)("<a %1$s>Work</a> by %2$s/ %3$s","caption"),d(r),s?u(s,f):f,o?u(`${o}?ref=openverse`,p):p):t?(0,c.sprintf)((0,c._x)('"%1$s"/ %2$s',"caption"),u(r,(0,l.decodeEntities)(t)),o?u(`${o}?ref=openverse`,p):p):(0,c.sprintf)((0,c._x)("<a %1$s>Work</a>/ %3$s","caption"),d(r),o?u(`${o}?ref=openverse`,p):p),g.replace(/\s{2}/g," ")},f=async(e={})=>(await(0,n.resolveSelect)(i.store).getMediaItems({...e,orderBy:e?.search?"relevance":"date"})).map((e=>({...e,alt:e.alt_text,url:e.source_url,previewUrl:e.media_details?.sizes?.medium?.source_url,caption:e.caption?.raw}))),g=[{name:"images",labels:{name:(0,c.__)("Images"),search_items:(0,c.__)("Search images")},mediaType:"image",fetch:async(e={})=>f({...e,media_type:"image"})},{name:"videos",labels:{name:(0,c.__)("Videos"),search_items:(0,c.__)("Search videos")},mediaType:"video",fetch:async(e={})=>f({...e,media_type:"video"})},{name:"audio",labels:{name:(0,c.__)("Audio"),search_items:(0,c.__)("Search audio")},mediaType:"audio",fetch:async(e={})=>f({...e,media_type:"audio"})},{name:"openverse",labels:{name:(0,c.__)("Openverse"),search_items:(0,c.__)("Search Openverse")},mediaType:"image",async fetch(e={}){const t={...e,mature:!1,excluded_source:"flickr,inaturalist,wikimedia",license:"pdm,cc0"},r={per_page:"page_size",search:"q"},n=new URL("https://api.openverse.engineering/v1/images/");Object.entries(t).forEach((([e,t])=>{const s=r[e]||e;n.searchParams.set(s,t)}));const s=await window.fetch(n,{headers:{"User-Agent":"WordPress/inserter-media-fetch"}});return(await s.json()).results.map((e=>({...e,title:e.title?.toLowerCase().startsWith("file:")?e.title.slice(5):e.title,sourceId:e.id,id:void 0,caption:p(e),previewUrl:e.thumbnail})))},getReportUrl:({sourceId:e})=>`https://wordpress.org/openverse/image/${e}/report/`,isExternalResource:!0}];function v(){var e,t;const{setIsInserterOpened:r}=(0,n.useDispatch)(a.M),{storedSettings:c,canvasMode:l,templateType:u}=(0,n.useSelect)((e=>{const{getSettings:t,getCanvasMode:n,getEditedPostType:s}=(0,o.T)(e(a.M));return{storedSettings:t(r),canvasMode:n(),templateType:s()}}),[r]),d=null!==(e=c.__experimentalAdditionalBlockPatterns)&&void 0!==e?e:c.__experimentalBlockPatterns,p=null!==(t=c.__experimentalAdditionalBlockPatternCategories)&&void 0!==t?t:c.__experimentalBlockPatternCategories,{restBlockPatterns:f,restBlockPatternCategories:v,templateSlug:m}=(0,n.useSelect)((e=>{const{getEditedPostType:t,getEditedPostId:r}=e(a.M),{getEditedEntityRecord:n}=e(i.store),s=n("postType",t(),r());return{restBlockPatterns:e(i.store).getBlockPatterns(),restBlockPatternCategories:e(i.store).getBlockPatternCategories(),templateSlug:s.slug}}),[]),y=function(e){const t=e?.match(/^(category|tag|taxonomy-([^-]+))$|^(((category|tag)|taxonomy-([^-]+))-(.+))$/);let r,s;return t&&(t[1]?r=t[2]?t[2]:t[1]:t[3]&&(r=t[6]?t[6]:t[4],s=t[7]),r="tag"===r?"post_tag":r),(0,n.useSelect)((e=>{const{getEntityRecords:t,getTaxonomy:n}=e(i.store);let a,o;if(r&&(a=n(r)?.labels?.singular_name),s){const e=t("taxonomy",r,{slug:s,per_page:1});e&&e[0]&&(o=e[0].name)}return{archiveTypeLabel:a,archiveNameLabel:o}}),[r,s])}(m),h=(0,s.useMemo)((()=>[...d||[],...f||[]].filter(((e,t,r)=>t===r.findIndex((t=>e.name===t.name)))).filter((({postTypes:e})=>!e||Array.isArray(e)&&e.includes(u)))),[d,f,u]),_=(0,s.useMemo)((()=>[...p||[],...v||[]].filter(((e,t,r)=>t===r.findIndex((t=>e.name===t.name))))),[p,v]);return(0,s.useMemo)((()=>{const{__experimentalAdditionalBlockPatterns:e,__experimentalAdditionalBlockPatternCategories:t,focusMode:r,...n}=c;return{...n,inserterMediaCategories:g,__experimentalBlockPatterns:h,__experimentalBlockPatternCategories:_,focusMode:("view"!==l||!r)&&r,__experimentalArchiveTitleTypeLabel:y.archiveTypeLabel,__experimentalArchiveTitleNameLabel:y.archiveNameLabel}}),[c,h,_,l,y.archiveTypeLabel,y.archiveNameLabel])}},95831:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(86087),s=r(56427);function i(){return(0,n.createElement)("div",{className:"edit-site-canvas-spinner"},(0,n.createElement)(s.Spinner,null))}},12467:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(86087),s=r(27723),i=r(52619),a=r(56427),o=r(94715),c=r(29491);function l({text:e,children:t}){const r=(0,c.useCopyToClipboard)(e);return(0,n.createElement)(a.Button,{variant:"secondary",ref:r},t)}function u({message:e,error:t}){const r=[(0,n.createElement)(l,{key:"copy-error",text:t.stack},(0,s.__)("Copy Error"))];return(0,n.createElement)(o.Warning,{className:"editor-error-boundary",actions:r},e)}class d extends n.Component{constructor(){super(...arguments),this.state={error:null}}componentDidCatch(e){(0,i.doAction)("editor.ErrorBoundary.errorLogged",e)}static getDerivedStateFromError(e){return{error:e}}render(){return this.state.error?(0,n.createElement)(u,{message:(0,s.__)("The editor has encountered an unexpected error."),error:this.state.error}):this.props.children}}},2923:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var n=r(86087),s=r(47143),i=r(94715),a=r(87214),o=r(70351);const{useGlobalStylesOutput:c}=(0,o.T)(i.privateApis);function l(){return function(){const[e,t]=c(),{getSettings:r}=(0,s.useSelect)(a.M),{updateSettings:i}=(0,s.useDispatch)(a.M);(0,n.useEffect)((()=>{var n;if(!e||!t)return;const s=r(),a=Object.values(null!==(n=s.styles)&&void 0!==n?n:[]).filter((e=>!e.isGlobalStyles));i({...s,styles:[...a,...e],__experimentalFeatures:t})}),[e,t])}(),null}},24443:(e,t,r)=>{"use strict";r.d(t,{T:()=>v,n:()=>g});var n=r(86087),s=r(76597),i=r.n(s);function a(e){return"[object Object]"===Object.prototype.toString.call(e)}function o(e){var t,r;return!1!==a(e)&&(void 0===(t=e.constructor)||!1!==a(r=t.prototype)&&!1!==r.hasOwnProperty("isPrototypeOf"))}var c=r(47143),l=r(3582),u=r(94715),d=r(70351);const{GlobalStylesContext:p,cleanEmptyObject:f}=(0,d.T)(u.privateApis);function g(e,t){return i()(e,t,{isMergeableObject:o})}function v({children:e}){const t=function(){const[e,t,r]=function(){const{globalStylesId:e,isReady:t,settings:r,styles:s}=(0,c.useSelect)((e=>{const{getEditedEntityRecord:t,hasFinishedResolution:r}=e(l.store),n=e(l.store).__experimentalGetCurrentGlobalStylesId(),s=n?t("root","globalStyles",n):void 0;let i=!1;return r("__experimentalGetCurrentGlobalStylesId")&&(i=!n||r("getEditedEntityRecord",["root","globalStyles",n])),{globalStylesId:n,isReady:i,settings:s?.settings,styles:s?.styles}}),[]),{getEditedEntityRecord:i}=(0,c.useSelect)(l.store),{editEntityRecord:a}=(0,c.useDispatch)(l.store);return[t,(0,n.useMemo)((()=>({settings:null!=r?r:{},styles:null!=s?s:{}})),[r,s]),(0,n.useCallback)(((t,r={})=>{var n,s;const o=i("root","globalStyles",e),c=t({styles:null!==(n=o?.styles)&&void 0!==n?n:{},settings:null!==(s=o?.settings)&&void 0!==s?s:{}});a("root","globalStyles",e,{styles:f(c.styles)||{},settings:f(c.settings)||{}},r)}),[e])]}(),[s,i]=function(){const e=(0,c.useSelect)((e=>e(l.store).__experimentalGetCurrentThemeBaseGlobalStyles()),[]);return[!!e,e]}(),a=(0,n.useMemo)((()=>i&&t?g(i,t):{}),[t,i]);return(0,n.useMemo)((()=>({isReady:e&&s,user:t,base:i,merged:a,setUserConfig:r})),[a,t,i,r,e,s])}();return t.isReady?(0,n.createElement)(p.Provider,{value:t},e):null}},34465:(e,t,r)=>{"use strict";r.d(t,{O:()=>c});var n=r(86087),s=r(47143),i=r(3582),a=r(71718);const o=1e4;function c(){const{isLoaded:e}=(0,a.A)(),[t,r]=(0,n.useState)(!1),c=(0,s.useSelect)((e=>{const r=e(i.store).hasResolvingSelectors();return!t&&!r}),[t]);return(0,n.useEffect)((()=>{let e;return t||(e=setTimeout((()=>{r(!0)}),o)),()=>{clearTimeout(e)}}),[t]),(0,n.useEffect)((()=>{if(c){const e=setTimeout((()=>{r(!0)}),1e3);return()=>{clearTimeout(e)}}}),[c]),!t||!e}},61288:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(86087),s=r(17697),i=r.n(s),a=r(56427);function o(e){return(0,n.createElement)(a.Button,{...e,className:i()("edit-site-sidebar-button",e.className)})}},59783:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(86087),s=r(17697),i=r.n(s),a=r(56427),o=r(27723);const c=(0,n.forwardRef)((function({icon:e,size:t=24,...r},s){return(0,n.cloneElement)(e,{width:t,height:t,...r,ref:s})}));var l=r(51609),u=r(5573);const d=(0,l.createElement)(u.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(u.Path,{d:"m13.1 16-3.4-4 3.4-4 1.1 1-2.6 3 2.6 3-1.1 1z"})),p=(0,l.createElement)(u.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(u.Path,{d:"M10.8622 8.04053L14.2805 12.0286L10.8622 16.0167L9.72327 15.0405L12.3049 12.0286L9.72327 9.01672L10.8622 8.04053Z"}));function f({className:e,icon:t,withChevron:r=!1,suffix:s,children:l,...u}){return(0,n.createElement)(a.__experimentalItem,{className:i()("edit-site-sidebar-navigation-item",{"with-suffix":!r&&s},e),...u},(0,n.createElement)(a.__experimentalHStack,{justify:"flex-start"},t&&(0,n.createElement)(c,{style:{fill:"currentcolor"},icon:t,size:24}),(0,n.createElement)(a.FlexBlock,null,l),r&&(0,n.createElement)(c,{icon:(0,o.isRTL)()?d:p,className:"edit-site-sidebar-navigation-item__drilldown-indicator",size:24}),!r&&s))}},23522:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(86087),s=r(17697),i=r.n(s),a=r(47143),o=r(56427),c=r(27723),l=r(51609),u=r(5573);const d=(0,l.createElement)(u.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24"},(0,l.createElement)(u.Path,{d:"M20 10c0-5.51-4.49-10-10-10C4.48 0 0 4.49 0 10c0 5.52 4.48 10 10 10 5.51 0 10-4.48 10-10zM7.78 15.37L4.37 6.22c.55-.02 1.17-.08 1.17-.08.5-.06.44-1.13-.06-1.11 0 0-1.45.11-2.37.11-.18 0-.37 0-.58-.01C4.12 2.69 6.87 1.11 10 1.11c2.33 0 4.45.87 6.05 2.34-.68-.11-1.65.39-1.65 1.58 0 .74.45 1.36.9 2.1.35.61.55 1.36.55 2.46 0 1.49-1.4 5-1.4 5l-3.03-8.37c.54-.02.82-.17.82-.17.5-.05.44-1.25-.06-1.22 0 0-1.44.12-2.38.12-.87 0-2.33-.12-2.33-.12-.5-.03-.56 1.2-.06 1.22l.92.08 1.26 3.41zM17.41 10c.24-.64.74-1.87.43-4.25.7 1.29 1.05 2.71 1.05 4.25 0 3.29-1.73 6.24-4.4 7.78.97-2.59 1.94-5.2 2.92-7.78zM6.1 18.09C3.12 16.65 1.11 13.53 1.11 10c0-1.3.23-2.48.72-3.59C3.25 10.3 4.67 14.2 6.1 18.09zm4.03-6.63l2.58 6.98c-.86.29-1.76.45-2.71.45-.79 0-1.57-.11-2.29-.33.81-2.38 1.62-4.74 2.42-7.1z"}));var p=r(3582);const f=function({className:e}){const{isRequestingSite:t,siteIconUrl:r}=(0,a.useSelect)((e=>{const{getEntityRecord:t}=e(p.store),r=t("root","__unstableBase",void 0);return{isRequestingSite:!r,siteIconUrl:r?.site_icon_url}}),[]);if(t&&!r)return(0,n.createElement)("div",{className:"edit-site-site-icon__image"});const s=r?(0,n.createElement)("img",{className:"edit-site-site-icon__image",alt:(0,c.__)("Site Icon"),src:r}):(0,n.createElement)(o.Icon,{className:"edit-site-site-icon__icon",size:"48px",icon:d});return(0,n.createElement)("div",{className:i()(e,"edit-site-site-icon")},s)}},18890:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(86087),s=r(47143),i=r(3582),a=r(71628),o=r(87214),c=r(70351),l=r(54210);const{useLocation:u}=(0,c.T)(a.privateApis);function d(){const{params:e}=u(),{postType:t}=e,r=(0,l.A)(e?.postId),{isRequestingSite:a,homepageId:c,url:d}=(0,s.useSelect)((e=>{const{getSite:t,getUnstableBase:r}=e(i.store),n=t(),s=r();return{isRequestingSite:!s,homepageId:"page"===n?.show_on_front?n.page_on_front:null,url:s?.home}}),[]),{setEditedEntity:p,setTemplate:f,setTemplatePart:g,setPage:v,setNavigationMenu:m}=(0,s.useDispatch)(o.M);(0,n.useEffect)((()=>{if(t&&r)switch(t){case"wp_template":f(r);break;case"wp_template_part":g(r);break;case"wp_navigation":m(r);break;case"wp_block":p(t,r);break;default:v({context:{postType:t,postId:r}})}else c?v({context:{postType:"page",postId:Number(c)}}):a||v({path:d})}),[d,r,t,c,a,p,v,f,g,m])}},71718:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(47143),s=r(3582),i=r(43656),a=r(18537),o=r(87214),c=r(54210);function l(e,t){const{record:r,title:l,description:u,isLoaded:d,icon:p}=(0,n.useSelect)((r=>{const{getEditedPostType:n,getEditedPostId:a}=r(o.M),{getEditedEntityRecord:l,hasFinishedResolution:u}=r(s.store),{__experimentalGetTemplateInfo:d}=r(i.store),p=null!=e?e:n();let f=null!=t?t:a();f=(0,c.A)(f,p);const g=l("postType",p,f),v=f&&u("getEditedEntityRecord",["postType",p,f]),m=d(g);return{record:g,title:m.title,description:m.description,isLoaded:v,icon:m.icon}}),[e,t]);return{isLoaded:d,icon:p,record:r,getTitle:()=>l?(0,a.decodeEntities)(l):null,getDescription:()=>u?(0,a.decodeEntities)(u):null}}},70351:(e,t,r)=>{"use strict";r.d(t,{T:()=>a});var n=r(35434);const s={6.4:"I know using unstable features means my plugin or theme will inevitably break on the next WordPress release.",6.5:"I know using unstable features means my theme or plugin will inevitably break in the next version of WordPress.",6.6:"I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress."},{lock:i,unlock:a}=function(){let e;for(const t of Object.values(s))try{return(0,n.__dangerousOptInToUnstableAPIsOnlyForCoreModules)(t,"@wordpress/edit-site")}catch(t){e=t}throw e}()},87214:(e,t,r)=>{"use strict";r.d(t,{M:()=>Oe});var n={};r.r(n),r.d(n,{__experimentalSetPreviewDeviceType:()=>x,addTemplate:()=>A,closeGeneralSidebar:()=>$,openGeneralSidebar:()=>U,openNavigationPanelToMenu:()=>j,removeTemplate:()=>O,revertTemplate:()=>F,setEditedEntity:()=>C,setEditedPostContext:()=>k,setHasPageContentFocus:()=>W,setHomeTemplateId:()=>N,setIsInserterOpened:()=>D,setIsListViewOpened:()=>z,setIsNavigationPanelOpened:()=>L,setIsSaveViewOpened:()=>G,setNavigationMenu:()=>M,setNavigationPanelActiveMenu:()=>V,setPage:()=>R,setTemplate:()=>P,setTemplatePart:()=>I,switchEditorMode:()=>H,toggleFeature:()=>T,updateSettings:()=>B});var s={};r.r(s),r.d(s,{setCanvasMode:()=>q,setEditorCanvasContainerView:()=>Z});var i={};r.r(i),r.d(i,{__experimentalGetInsertionPoint:()=>ge,__experimentalGetPreviewDeviceType:()=>se,__unstableGetPreference:()=>re,getCanUserCreateMedia:()=>ie,getCurrentTemplateNavigationPanelSubMenu:()=>_e,getCurrentTemplateTemplateParts:()=>ye,getEditedPostContext:()=>de,getEditedPostId:()=>ue,getEditedPostType:()=>le,getEditorMode:()=>he,getHomeTemplateId:()=>ce,getNavigationPanelActiveMenu:()=>Ee,getPage:()=>pe,getReusableBlocks:()=>ae,getSettings:()=>oe,hasPageContentFocus:()=>we,isFeatureActive:()=>ne,isInserterOpened:()=>fe,isListViewOpened:()=>ve,isNavigationOpened:()=>be,isPage:()=>Se,isSaveViewOpened:()=>me});var a={};r.r(a),r.d(a,{getCanvasMode:()=>Te,getEditorCanvasContainerView:()=>xe});var o=r(47143);const c=(0,o.combineReducers)({deviceType:function(e="Desktop",t){return"SET_PREVIEW_DEVICE_TYPE"===t.type?t.deviceType:e},settings:function(e={},t){return"UPDATE_SETTINGS"===t.type?{...e,...t.settings}:e},editedPost:function(e={},t){switch(t.type){case"SET_EDITED_POST":return{postType:t.postType,id:t.id,context:t.context};case"SET_EDITED_POST_CONTEXT":return{...e,context:t.context}}return e},blockInserterPanel:function(e=!1,t){switch(t.type){case"SET_IS_LIST_VIEW_OPENED":return!t.isOpen&&e;case"SET_IS_INSERTER_OPENED":return t.value;case"SET_CANVAS_MODE":return!1}return e},listViewPanel:function(e=!1,t){switch(t.type){case"SET_IS_INSERTER_OPENED":return!t.value&&e;case"SET_IS_LIST_VIEW_OPENED":return t.isOpen;case"SET_CANVAS_MODE":return!1}return e},saveViewPanel:function(e=!1,t){switch(t.type){case"SET_IS_SAVE_VIEW_OPENED":return t.isOpen;case"SET_CANVAS_MODE":return!1}return e},canvasMode:function(e="init",t){return"SET_CANVAS_MODE"===t.type?t.mode:e},editorCanvasContainerView:function(e=void 0,t){return"SET_EDITOR_CANVAS_CONTAINER_VIEW"===t.type?t.view:e},hasPageContentFocus:function(e=!1,t){switch(t.type){case"SET_EDITED_POST":return!!t.context?.postId;case"SET_HAS_PAGE_CONTENT_FOCUS":return t.hasPageContentFocus}return e}});var l=r(1455),u=r.n(l),d=r(74997),p=r(64040),f=r.n(p),g=r(93832),v=r(27723),m=r(692),y=r(3582),h=r(93090),_=r(94715),E=r(20195),b=r(41233),S=r(18537);const w="core/edit-site";function T(e){return function({registry:t}){f()("select( 'core/edit-site' ).toggleFeature( featureName )",{since:"6.0",alternative:"select( 'core/preferences').toggle( 'core/edit-site', featureName )"}),t.dispatch(b.store).toggle("core/edit-site",e)}}function x(e){return{type:"SET_PREVIEW_DEVICE_TYPE",deviceType:e}}const P=(e,t)=>async({dispatch:r,registry:n})=>{if(!t)try{const r=await n.resolveSelect(y.store).getEntityRecord("postType","wp_template",e);t=r?.slug}catch(e){}r({type:"SET_EDITED_POST",postType:"wp_template",id:e,context:{templateSlug:t}})},A=e=>async({dispatch:t,registry:r})=>{const n=await r.dispatch(y.store).saveEntityRecord("postType","wp_template",e);e.content&&r.dispatch(y.store).editEntityRecord("postType","wp_template",n.id,{blocks:(0,d.parse)(e.content)},{undoIgnore:!0}),t({type:"SET_EDITED_POST",postType:"wp_template",id:n.id,context:{templateSlug:n.slug}})},O=e=>async({registry:t})=>{try{await t.dispatch(y.store).deleteEntityRecord("postType",e.type,e.id,{force:!0});const r=t.select(y.store).getLastEntityDeleteError("postType",e.type,e.id);if(r)throw r;const n="string"==typeof e.title?e.title:e.title?.rendered;t.dispatch(m.store).createSuccessNotice((0,v.sprintf)((0,v.__)('"%s" deleted.'),(0,S.decodeEntities)(n)),{type:"snackbar",id:"site-editor-template-deleted-success"})}catch(e){const r=e.message&&"unknown_error"!==e.code?e.message:(0,v.__)("An error occurred while deleting the template.");t.dispatch(m.store).createErrorNotice(r,{type:"snackbar"})}};function I(e){return{type:"SET_EDITED_POST",postType:"wp_template_part",id:e}}function M(e){return{type:"SET_EDITED_POST",postType:"wp_navigation",id:e}}function C(e,t){return{type:"SET_EDITED_POST",postType:e,id:t}}function N(){return f()("dispatch( 'core/edit-site' ).setHomeTemplateId",{since:"6.2",version:"6.4"}),{type:"NOTHING"}}function k(e){return{type:"SET_EDITED_POST_CONTEXT",context:e}}const R=e=>async({dispatch:t,registry:r})=>{if(!e.path&&e.context?.postId){const t=await r.resolveSelect(y.store).getEntityRecord("postType",e.context.postType||"post",e.context.postId);e.path=(0,g.getPathAndQueryString)(t?.link)}let n;try{n=await u()({url:(0,g.addQueryArgs)(e.path,{"_wp-find-template":!0})}).then((({data:e})=>e))}catch(e){}const s=await r.resolveSelect(y.store).getEntityRecord("postType","wp_template",n.id);if(s)return t({type:"SET_EDITED_POST",postType:"wp_template",id:s.id,context:{...e.context,templateSlug:s.slug}}),s.id};function V(){return f()("dispatch( 'core/edit-site' ).setNavigationPanelActiveMenu",{since:"6.2",version:"6.4"}),{type:"NOTHING"}}function j(){return f()("dispatch( 'core/edit-site' ).openNavigationPanelToMenu",{since:"6.2",version:"6.4"}),{type:"NOTHING"}}function L(){return f()("dispatch( 'core/edit-site' ).setIsNavigationPanelOpened",{since:"6.2",version:"6.4"}),{type:"NOTHING"}}function D(e){return{type:"SET_IS_INSERTER_OPENED",value:e}}function B(e){return{type:"UPDATE_SETTINGS",settings:e}}function z(e){return{type:"SET_IS_LIST_VIEW_OPENED",isOpen:e}}function G(e){return{type:"SET_IS_SAVE_VIEW_OPENED",isOpen:e}}const F=(e,{allowUndo:t=!0}={})=>async({registry:r})=>{const n="edit-site-template-reverted";if(r.dispatch(m.store).removeNotice(n),function(e){return!!e&&"custom"===e?.source&&e?.has_theme_file}(e))try{const s=r.select(y.store).getEntityConfig("postType",e.type);if(!s)return void r.dispatch(m.store).createErrorNotice((0,v.__)("The editor has encountered an unexpected error. Please reload."),{type:"snackbar"});const i=(0,g.addQueryArgs)(`${s.baseURL}/${e.id}`,{context:"edit",source:"theme"}),a=await u()({path:i});if(!a)return void r.dispatch(m.store).createErrorNotice((0,v.__)("The editor has encountered an unexpected error. Please reload."),{type:"snackbar"});const o=({blocks:e=[]})=>(0,d.__unstableSerializeAndClean)(e),c=r.select(y.store).getEditedEntityRecord("postType",e.type,e.id);r.dispatch(y.store).editEntityRecord("postType",e.type,e.id,{content:o,blocks:c.blocks,source:"custom"},{undoIgnore:!0});const l=(0,d.parse)(a?.content?.raw);if(r.dispatch(y.store).editEntityRecord("postType",e.type,a.id,{content:o,blocks:l,source:"theme"}),t){const t=()=>{r.dispatch(y.store).editEntityRecord("postType",e.type,c.id,{content:o,blocks:c.blocks,source:"custom"})};r.dispatch(m.store).createSuccessNotice((0,v.__)("Template reverted."),{type:"snackbar",id:n,actions:[{label:(0,v.__)("Undo"),onClick:t}]})}}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,v.__)("Template revert failed. Please reload.");r.dispatch(m.store).createErrorNotice(t,{type:"snackbar"})}else r.dispatch(m.store).createErrorNotice((0,v.__)("This template is not revertable."),{type:"snackbar"})},U=e=>({registry:t})=>{t.dispatch(h.M_).enableComplementaryArea(w,e)},$=()=>({registry:e})=>{e.dispatch(h.M_).disableComplementaryArea(w)},H=e=>({registry:t})=>{t.dispatch("core/preferences").set("core/edit-site","editorMode",e),"visual"!==e&&t.dispatch(_.store).clearSelectedBlock(),"visual"===e?(0,E.speak)((0,v.__)("Visual editor selected"),"assertive"):"text"===e&&(0,E.speak)((0,v.__)("Code editor selected"),"assertive")},W=e=>({dispatch:t,registry:r})=>{e&&r.dispatch(_.store).clearSelectedBlock(),t({type:"SET_HAS_PAGE_CONTENT_FOCUS",hasPageContentFocus:e})},q=e=>({registry:t,dispatch:r,select:n})=>{t.dispatch(_.store).__unstableSetEditorMode("edit"),r({type:"SET_CANVAS_MODE",mode:e}),"edit"===e&&t.select(b.store).get("core/edit-site","showListViewByDefault")&&r.setIsListViewOpened(!0),"view"===e&&n.isPage()&&r.setHasPageContentFocus(!0)},Z=e=>({dispatch:t})=>{t({type:"SET_EDITOR_CANVAS_CONTAINER_VIEW",view:e})};var Y={};function J(e){return[e]}function Q(e,t,r){var n;if(e.length!==t.length)return!1;for(n=r;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}var X=r(16480),K=r(86087);const ee=[],te=function(e,t){var r,n,s=0;function i(){var i,a,o=r,c=arguments.length;e:for(;o;){if(o.args.length===arguments.length){for(a=0;a<c;a++)if(o.args[a]!==arguments[a]){o=o.next;continue e}return o!==r&&(o===n&&(n=o.prev),o.prev.next=o.next,o.next&&(o.next.prev=o.prev),o.next=r,o.prev=null,r.prev=o,r=o),o.val}o=o.next}for(i=new Array(c),a=0;a<c;a++)i[a]=arguments[a];return o={args:i,val:e.apply(null,i)},r?(r.prev=o,o.next=r):n=o,s===t.maxSize?(n=n.prev).next=null:s++,r=o,o.val}return t=t||{},i.clear=function(){r=null,n=null,s=0},i}((function(e=ee,t){const r=t?t.reduce(((e,t)=>({...e,[t.id]:t})),{}):{},n=[],s=[...e];for(;s.length;){const{innerBlocks:e,...t}=s.shift();if(s.unshift(...e),(0,d.isTemplatePart)(t)){const{attributes:{theme:e,slug:s}}=t,i=r[`${e}//${s}`];i&&n.push({templatePart:i,block:t})}}return n})),re=(0,o.createRegistrySelector)((e=>(t,r)=>e(b.store).get("core/edit-site",r)));function ne(e,t){return f()("select( 'core/edit-site' ).isFeatureActive",{since:"6.0",alternative:"select( 'core/preferences' ).get"}),!!re(e,t)}function se(e){return e.deviceType}const ie=(0,o.createRegistrySelector)((e=>()=>e(y.store).canUser("create","media"))),ae=(0,o.createRegistrySelector)((e=>()=>"web"===K.Platform.OS?e(y.store).getEntityRecords("postType","wp_block",{per_page:-1}):[])),oe=function(e){var t,r=(e=>[ie(e),e.settings,re(e,"focusMode"),re(e,"distractionFree"),re(e,"fixedToolbar"),re(e,"keepCaretInsideBlock"),re(e,"showIconLabels"),ae(e),le(e)])||J;function n(){t=new WeakMap}function s(){var n,s,i,a,o,c=arguments.length;for(a=new Array(c),i=0;i<c;i++)a[i]=arguments[i];for(n=function(e){var r,n,s,i,a,o=t,c=!0;for(r=0;r<e.length;r++){if(!(a=n=e[r])||"object"!=typeof a){c=!1;break}o.has(n)?o=o.get(n):(s=new WeakMap,o.set(n,s),o=s)}return o.has(Y)||((i=function(){var e={clear:function(){e.head=null}};return e}()).isUniqueByDependants=c,o.set(Y,i)),o.get(Y)}(o=r.apply(null,a)),n.isUniqueByDependants||(n.lastDependants&&!Q(o,n.lastDependants,0)&&n.clear(),n.lastDependants=o),s=n.head;s;){if(Q(s.args,a,1))return s!==n.head&&(s.prev.next=s.next,s.next&&(s.next.prev=s.prev),s.next=n.head,s.prev=null,n.head.prev=s,n.head=s),s.val;s=s.next}return s={val:e.apply(null,a)},a[0]=null,s.args=a,n.head&&(n.head.prev=s,s.next=n.head),n.head=s,s.val}return s.getDependants=r,s.clear=n,n(),s}(((e,t)=>{const r={...e.settings,outlineMode:!0,focusMode:!!re(e,"focusMode"),isDistractionFree:!!re(e,"distractionFree"),hasFixedToolbar:!!re(e,"fixedToolbar"),keepCaretInsideBlock:!!re(e,"keepCaretInsideBlock"),showIconLabels:!!re(e,"showIconLabels"),__experimentalSetIsInserterOpened:t,__experimentalReusableBlocks:ae(e),__experimentalPreferPatternsOnRoot:"wp_template"===le(e)};return ie(e)?(r.mediaUpload=({onError:t,...r})=>{(0,X.uploadMedia)({wpAllowedMimeTypes:e.settings.allowedMimeTypes,onError:({message:e})=>t(e),...r})},r):r}));function ce(){f()("select( 'core/edit-site' ).getHomeTemplateId",{since:"6.2",version:"6.4"})}function le(e){return e.editedPost.postType}function ue(e){return e.editedPost.id}function de(e){return e.editedPost.context}function pe(e){return{context:e.editedPost.context}}function fe(e){return!!e.blockInserterPanel}const ge=(0,o.createRegistrySelector)((e=>t=>{if("object"==typeof t.blockInserterPanel){const{rootClientId:e,insertionIndex:r,filterValue:n}=t.blockInserterPanel;return{rootClientId:e,insertionIndex:r,filterValue:n}}if(we(t)){const[t]=e(_.store).__experimentalGetGlobalBlocksByName("core/post-content");if(t)return{rootClientId:t,insertionIndex:void 0,filterValue:void 0}}return{rootClientId:void 0,insertionIndex:void 0,filterValue:void 0}}));function ve(e){return e.listViewPanel}function me(e){return e.saveViewPanel}const ye=(0,o.createRegistrySelector)((e=>t=>{const r=le(t),n=ue(t),s=e(y.store).getEditedEntityRecord("postType",r,n),i=e(y.store).getEntityRecords("postType","wp_template_part",{per_page:-1});return te(s.blocks,i)}));function he(e){return re(e,"editorMode")}function _e(){f()("dispatch( 'core/edit-site' ).getCurrentTemplateNavigationPanelSubMenu",{since:"6.2",version:"6.4"})}function Ee(){f()("dispatch( 'core/edit-site' ).getNavigationPanelActiveMenu",{since:"6.2",version:"6.4"})}function be(){f()("dispatch( 'core/edit-site' ).isNavigationOpened",{since:"6.2",version:"6.4"})}function Se(e){return!!e.editedPost.context?.postId}function we(e){return!!Se(e)&&e.hasPageContentFocus}function Te(e){return e.canvasMode}function xe(e){return e.editorCanvasContainerView}var Pe=r(70351);const Ae={reducer:c,actions:n,selectors:i},Oe=(0,o.createReduxStore)(w,Ae);(0,o.register)(Oe),(0,Pe.T)(Oe).registerPrivateSelectors(a),(0,Pe.T)(Oe).registerPrivateActions(s)},54210:(e,t,r)=>{"use strict";function n(e){return/^\s*\d+\s*$/.test(e)&&(e=Number(e)),e}r.d(t,{A:()=>n})},44412:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M4 20h8v-1.5H4V20zM18.9 3.5c-.6-.6-1.5-.6-2.1 0l-7.2 7.2c-.4-.1-.7 0-1.1.1-.5.2-1.5.7-1.9 2.2-.4 1.7-.8 2.2-1.1 2.7-.1.1-.2.3-.3.4l-.6 1.1H6c2 0 3.4-.4 4.7-1.4.8-.6 1.2-1.4 1.3-2.3 0-.3 0-.5-.1-.7L19 5.7c.5-.6.5-1.6-.1-2.2zM9.7 14.7c-.7.5-1.5.8-2.4 1 .2-.5.5-1.2.8-2.3.2-.6.4-1 .8-1.1.5-.1 1 .1 1.3.3.2.2.3.5.2.8 0 .3-.1.9-.7 1.3z"})})},46445:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})})},45260:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})})},48443:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)(n.Path,{d:"M17.2 10.9c-.5-1-1.2-2.1-2.1-3.2-.6-.9-1.3-1.7-2.1-2.6L12 4l-1 1.1c-.6.9-1.3 1.7-2 2.6-.8 1.2-1.5 2.3-2 3.2-.6 1.2-1 2.2-1 3 0 3.4 2.7 6.1 6.1 6.1s6.1-2.7 6.1-6.1c0-.8-.3-1.8-1-3zm-5.1 7.6c-2.5 0-4.6-2.1-4.6-4.6 0-.3.1-1 .8-2.3.5-.9 1.1-1.9 2-3.1.7-.9 1.3-1.7 1.8-2.3.7.8 1.3 1.6 1.8 2.3.8 1.1 1.5 2.2 2 3.1.7 1.3.8 2 .8 2.3 0 2.5-2.1 4.6-4.6 4.6z"})})},60649:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{fillRule:"evenodd",d:"M18 5.5h-8v8h8.5V6a.5.5 0 00-.5-.5zm-9.5 8h-3V6a.5.5 0 01.5-.5h2.5v8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})})},31667:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M18.5 10.5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})})},31613:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M12 4L4 7.9V20h16V7.9L12 4zm6.5 14.5H14V13h-4v5.5H5.5V8.8L12 5.7l6.5 3.1v9.7z"})})},38150:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"})})},59613:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M12 3c-5 0-9 4-9 9s4 9 9 9 9-4 9-9-4-9-9-9zm0 1.5c4.1 0 7.5 3.4 7.5 7.5v.1c-1.4-.8-3.3-1.7-3.4-1.8-.2-.1-.5-.1-.8.1l-2.9 2.1L9 11.3c-.2-.1-.4 0-.6.1l-3.7 2.2c-.1-.5-.2-1-.2-1.5 0-4.2 3.4-7.6 7.5-7.6zm0 15c-3.1 0-5.7-1.9-6.9-4.5l3.7-2.2 3.5 1.2c.2.1.5 0 .7-.1l2.9-2.1c.8.4 2.5 1.2 3.5 1.9-.9 3.3-3.9 5.8-7.4 5.8z"})})},33484:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M4.75 4a.75.75 0 0 0-.75.75v7.826c0 .2.08.39.22.53l6.72 6.716a2.313 2.313 0 0 0 3.276-.001l5.61-5.611-.531-.53.532.528a2.315 2.315 0 0 0 0-3.264L13.104 4.22a.75.75 0 0 0-.53-.22H4.75ZM19 12.576a.815.815 0 0 1-.236.574l-5.61 5.611a.814.814 0 0 1-1.153 0L5.5 12.264V5.5h6.763l6.5 6.502a.816.816 0 0 1 .237.574ZM8.75 9.75a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"})})},39194:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})})},25031:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M6.9 7L3 17.8h1.7l1-2.8h4.1l1 2.8h1.7L8.6 7H6.9zm-.7 6.6l1.5-4.3 1.5 4.3h-3zM21.6 17c-.1.1-.2.2-.3.2-.1.1-.2.1-.4.1s-.3-.1-.4-.2c-.1-.1-.1-.3-.1-.6V12c0-.5 0-1-.1-1.4-.1-.4-.3-.7-.5-1-.2-.2-.5-.4-.9-.5-.4 0-.8-.1-1.3-.1s-1 .1-1.4.2c-.4.1-.7.3-1 .4-.2.2-.4.3-.6.5-.1.2-.2.4-.2.7 0 .*******.*******.******* 0 .6-.1.8-.3.2-.2.3-.4.3-.7 0-.3-.1-.5-.2-.7-.2-.2-.4-.3-.6-.4.2-.2.4-.3.7-.4.3-.1.6-.1.8-.1.3 0 .6 0 .*******.*******.*******.2.9v1.1c0 .3-.1.5-.3.6-.2.2-.5.3-.9.4-.3.1-.7.3-1.1.4-.4.1-.8.3-1.1.5-.3.2-.6.4-.8.7-.2.3-.3.7-.3 1.2 0 .6.2 1.1.5 1.4.3.4.9.5 1.6.5.5 0 1-.1 1.4-.3.4-.2.8-.6 1.1-1.1 0 .4.1.7.3 1 .2.3.6.4 1.2.4.4 0 .7-.1.9-.2.2-.1.5-.3.7-.4h-.3zm-3-.9c-.2.4-.5.7-.8.8-.3.2-.6.2-.8.2-.4 0-.6-.1-.9-.3-.2-.2-.3-.6-.3-1.1 0-.5.1-.9.3-1.2s.5-.5.8-.7c.3-.2.7-.3 1-.5.3-.1.6-.3.7-.6v3.4z"})})},26967:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(5573),s=r(39793);const i=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M18.5 15v3.5H13V6.7l4.5 4.1 1-1.1-6.2-5.8-5.8 5.8 1 1.1 4-4v11.7h-6V15H4v5h16v-5z"})})},93090:(e,t,r)=>{"use strict";r.d(t,{Du:()=>g,s2:()=>p,M_:()=>V});var n={};r.r(n),r.d(n,{closeModal:()=>O,disableComplementaryArea:()=>b,enableComplementaryArea:()=>E,openModal:()=>A,pinItem:()=>S,setDefaultComplementaryArea:()=>_,setFeatureDefaults:()=>P,setFeatureValue:()=>x,toggleFeature:()=>T,unpinItem:()=>w});var s={};r.r(s),r.d(s,{getActiveComplementaryArea:()=>I,isComplementaryAreaLoading:()=>M,isFeatureActive:()=>N,isItemPinned:()=>C,isModalActive:()=>k});var i=r(51609),a=r(17697),o=r.n(a),c=r(86087),l=r(56427),u=r(27723),d=r(29491);function p({children:e,className:t,ariaLabel:r,as:n="div",...s}){return(0,i.createElement)(n,{className:o()("interface-navigable-region",t),"aria-label":r,role:"region",tabIndex:"-1",...s},e)}const f={hidden:{opacity:0},hover:{opacity:1,transition:{type:"tween",delay:.2,delayChildren:.2}},distractionFreeInactive:{opacity:1,transition:{delay:0}}},g=(0,c.forwardRef)((function({isDistractionFree:e,footer:t,header:r,editorNotices:n,sidebar:s,secondarySidebar:a,notices:g,content:v,actions:m,labels:y,className:h,enableRegionNavigation:_=!0,shortcuts:E},b){const S=(0,l.__unstableUseNavigateRegions)(E);!function(e){(0,c.useEffect)((()=>{const t=document&&document.querySelector(`html:not(.${e})`);if(t)return t.classList.toggle(e),()=>{t.classList.toggle(e)}}),[e])}("interface-interface-skeleton__html-container");const w={header:(0,u.__)("Header"),body:(0,u.__)("Content"),secondarySidebar:(0,u.__)("Block Library"),sidebar:(0,u.__)("Settings"),actions:(0,u.__)("Publish"),footer:(0,u.__)("Footer"),...y};return(0,i.createElement)("div",{..._?S:{},ref:(0,d.useMergeRefs)([b,_?S.ref:void 0]),className:o()(h,"interface-interface-skeleton",S.className,!!t&&"has-footer")},(0,i.createElement)("div",{className:"interface-interface-skeleton__editor"},!!r&&(0,i.createElement)(p,{as:l.__unstableMotion.div,className:"interface-interface-skeleton__header","aria-label":w.header,initial:e?"hidden":"distractionFreeInactive",whileHover:e?"hover":"distractionFreeInactive",animate:e?"hidden":"distractionFreeInactive",variants:f,transition:e?{type:"tween",delay:.8}:void 0},r),e&&(0,i.createElement)("div",{className:"interface-interface-skeleton__header"},n),(0,i.createElement)("div",{className:"interface-interface-skeleton__body"},!!a&&(0,i.createElement)(p,{className:"interface-interface-skeleton__secondary-sidebar",ariaLabel:w.secondarySidebar},a),!!g&&(0,i.createElement)("div",{className:"interface-interface-skeleton__notices"},g),(0,i.createElement)(p,{className:"interface-interface-skeleton__content",ariaLabel:w.body},v),!!s&&(0,i.createElement)(p,{className:"interface-interface-skeleton__sidebar",ariaLabel:w.sidebar},s),!!m&&(0,i.createElement)(p,{className:"interface-interface-skeleton__actions",ariaLabel:w.actions},m))),!!t&&(0,i.createElement)(p,{className:"interface-interface-skeleton__footer",ariaLabel:w.footer},t))}));var v=r(47143),m=r(64040),y=r.n(m),h=r(41233);const _=(e,t)=>({type:"SET_DEFAULT_COMPLEMENTARY_AREA",scope:e,area:t}),E=(e,t)=>({registry:r,dispatch:n})=>{t&&(r.select(h.store).get(e,"isComplementaryAreaVisible")||r.dispatch(h.store).set(e,"isComplementaryAreaVisible",!0),n({type:"ENABLE_COMPLEMENTARY_AREA",scope:e,area:t}))},b=e=>({registry:t})=>{t.select(h.store).get(e,"isComplementaryAreaVisible")&&t.dispatch(h.store).set(e,"isComplementaryAreaVisible",!1)},S=(e,t)=>({registry:r})=>{if(!t)return;const n=r.select(h.store).get(e,"pinnedItems");!0!==n?.[t]&&r.dispatch(h.store).set(e,"pinnedItems",{...n,[t]:!0})},w=(e,t)=>({registry:r})=>{if(!t)return;const n=r.select(h.store).get(e,"pinnedItems");r.dispatch(h.store).set(e,"pinnedItems",{...n,[t]:!1})};function T(e,t){return function({registry:r}){y()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),r.dispatch(h.store).toggle(e,t)}}function x(e,t,r){return function({registry:n}){y()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),n.dispatch(h.store).set(e,t,!!r)}}function P(e,t){return function({registry:r}){y()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),r.dispatch(h.store).setDefaults(e,t)}}function A(e){return{type:"OPEN_MODAL",name:e}}function O(){return{type:"CLOSE_MODAL"}}const I=(0,v.createRegistrySelector)((e=>(t,r)=>{const n=e(h.store).get(r,"isComplementaryAreaVisible");if(void 0!==n)return!1===n?null:t?.complementaryAreas?.[r]})),M=(0,v.createRegistrySelector)((e=>(t,r)=>{const n=e(h.store).get(r,"isComplementaryAreaVisible"),s=t?.complementaryAreas?.[r];return n&&void 0===s})),C=(0,v.createRegistrySelector)((e=>(t,r,n)=>{var s;const i=e(h.store).get(r,"pinnedItems");return null===(s=i?.[n])||void 0===s||s})),N=(0,v.createRegistrySelector)((e=>(t,r,n)=>(y()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(h.store).get(r,n))));function k(e,t){return e.activeModal===t}const R=(0,v.combineReducers)({complementaryAreas:function(e={},t){switch(t.type){case"SET_DEFAULT_COMPLEMENTARY_AREA":{const{scope:r,area:n}=t;return e[r]?e:{...e,[r]:n}}case"ENABLE_COMPLEMENTARY_AREA":{const{scope:r,area:n}=t;return{...e,[r]:n}}}return e},activeModal:function(e=null,t){switch(t.type){case"OPEN_MODAL":return t.name;case"CLOSE_MODAL":return null}return e}}),V=(0,v.createReduxStore)("core/interface",{reducer:R,actions:n,selectors:s});(0,v.register)(V)},3472:(e,t,r)=>{"use strict";r.d(t,{z:()=>g});var n=r(51609),s=r(74590),i=r(90504),a=r(88673),o=r(96796);function c(e){var t=n.useRef();return t.current||(t.current={v:e()}),t.current.v}var l=function(){return l=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var s in t=arguments[r])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},l.apply(this,arguments)},u=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(n=Object.getOwnPropertySymbols(e);s<n.length;s++)t.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]])}return r};var d=r(21729),p=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,s,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){s={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(s)throw s.error}}return a};function f(e){return e}function g(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var g=p(t,1)[0],v=void 0===g?{}:g,m=function(e,t){var r=c((function(){return"function"==typeof e?e():e})),n=t.context,s=t.guards,a=t.actions,d=t.activities,p=t.services,f=t.delays,g=(t.state,u(t,["context","guards","actions","activities","services","delays","state"])),v=c((function(){var e={context:n,guards:s,actions:a,activities:d,services:p,delays:f},t=r.withConfig(e,(function(){return l(l({},r.context),n)}));return(0,i.U4)(t,g)}));return(0,o.A)((function(){Object.assign(v.machine.options.actions,a),Object.assign(v.machine.options.guards,s),Object.assign(v.machine.options.activities,d),Object.assign(v.machine.options.services,p),Object.assign(v.machine.options.delays,f)}),[a,s,d,p,f]),v}(e,v),y=(0,n.useCallback)((function(){return m.status===i.vy.NotStarted?v.state?a.Uw.create(v.state):m.machine.initialState:m.getSnapshot()}),[m]),h=(0,n.useCallback)((function(e,t){return(0,d.C3)(m,e,t)}),[m]),_=(0,n.useCallback)((function(e){return m.subscribe(e).unsubscribe}),[m]),E=(0,s.useSyncExternalStoreWithSelector)(_,y,y,f,h);return(0,n.useEffect)((function(){var e=v.state;return m.start(e?a.Uw.create(e):void 0),function(){m.stop(),m.status=i.vy.NotStarted}}),[]),[E,m.send,m]}},55739:(e,t,r)=>{"use strict";r.d(t,{d:()=>c});var n=r(51609),s=r(74590);r(96796);var i=r(21729),a=function(e,t){return e===t},o=function(e,t){if((0,i.kd)(e)){if(0===e.status&&t.current)return t.current;var r=(0,i.w6)(e);return t.current=0===e.status?r:null,r}return"state"in e?e.state:void 0};function c(e,t,r,i){void 0===r&&(r=a);var c=(0,n.useRef)(null),l=(0,n.useCallback)((function(t){return e.subscribe(t).unsubscribe}),[e]),u=(0,n.useCallback)((function(){return i?i(e):o(e,c)}),[e,i]);return(0,s.useSyncExternalStoreWithSelector)(l,u,u,t,r)}},21729:(e,t,r)=>{"use strict";r.d(t,{C3:()=>a,kd:()=>i,w6:()=>s});var n=r(90504);function s(e){return 0!==e.status?e.getSnapshot():e.machine.initialState}function i(e){return"state"in e&&"machine"in e}function a(e,t,r){if(e.status===n.vy.NotStarted)return!0;var s=void 0===r.changed&&(Object.keys(r.children).length>0||"boolean"==typeof t.changed);return!(r.changed||s)}},17697:(e,t)=>{var r;!function(){"use strict";var n={}.hasOwnProperty;function s(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var i=typeof r;if("string"===i||"number"===i)e.push(r);else if(Array.isArray(r)){if(r.length){var a=s.apply(null,r);a&&e.push(a)}}else if("object"===i){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){e.push(r.toString());continue}for(var o in r)n.call(r,o)&&r[o]&&e.push(o)}}}return e.join(" ")}e.exports?(s.default=s,e.exports=s):void 0===(r=function(){return s}.apply(t,[]))||(e.exports=r)}()},76597:e=>{"use strict";var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===r}(e)}(e)},r="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?o((r=e,Array.isArray(r)?[]:{}),e,t):e;var r}function s(e,t,r){return e.concat(t).map((function(e){return n(e,r)}))}function i(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function a(e,t){try{return t in e}catch(e){return!1}}function o(e,r,c){(c=c||{}).arrayMerge=c.arrayMerge||s,c.isMergeableObject=c.isMergeableObject||t,c.cloneUnlessOtherwiseSpecified=n;var l=Array.isArray(r);return l===Array.isArray(e)?l?c.arrayMerge(e,r,c):function(e,t,r){var s={};return r.isMergeableObject(e)&&i(e).forEach((function(t){s[t]=n(e[t],r)})),i(t).forEach((function(i){(function(e,t){return a(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,i)||(a(e,i)&&r.isMergeableObject(t[i])?s[i]=function(e,t){if(!t.customMerge)return o;var r=t.customMerge(e);return"function"==typeof r?r:o}(i,r)(e[i],t[i],r):s[i]=n(t[i],r))})),s}(e,r,c):n(r,c)}o.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,r){return o(e,r,t)}),{})};var c=o;e.exports=c},94736:(e,t,r)=>{"use strict";t.A=function(e){var t=e.size,r=void 0===t?24:t,n=e.onClick,o=(e.icon,e.className),c=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],0<=t.indexOf(r)||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],0<=t.indexOf(r)||Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,i),l=["gridicon","gridicons-notice-outline",o,!!function(e){return 0==e%18}(r)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return s.default.createElement("svg",a({className:l,height:r,width:r,onClick:n},c,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),s.default.createElement("g",null,s.default.createElement("path",{d:"M12 4c4.411 0 8 3.589 8 8s-3.589 8-8 8-8-3.589-8-8 3.589-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-2-2h2l.5-6h-3l.5 6z"})))};var n,s=(n=r(51609))&&n.__esModule?n:{default:n},i=["size","onClick","icon","className"];function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t,r=1;r<arguments.length;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},a.apply(this,arguments)}},84343:e=>{function t(e,t){if((e=e.replace(/\s+/g,""))===(t=t.replace(/\s+/g,"")))return 1;if(e.length<2||t.length<2)return 0;let r=new Map;for(let t=0;t<e.length-1;t++){const n=e.substring(t,t+2),s=r.has(n)?r.get(n)+1:1;r.set(n,s)}let n=0;for(let e=0;e<t.length-1;e++){const s=t.substring(e,e+2),i=r.has(s)?r.get(s):0;i>0&&(r.set(s,i-1),n++)}return 2*n/(e.length+t.length-2)}e.exports={compareTwoStrings:t,findBestMatch:function(e,r){if(!function(e,t){return"string"==typeof e&&!!Array.isArray(t)&&!!t.length&&!t.find((function(e){return"string"!=typeof e}))}(e,r))throw new Error("Bad arguments: First argument should be a string, second should be an array of strings");const n=[];let s=0;for(let i=0;i<r.length;i++){const a=r[i],o=t(e,a);n.push({target:a,rating:o}),o>n[s].rating&&(s=i)}return{ratings:n,bestMatch:n[s],bestMatchIndex:s}}}},96796:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});const n=r(51609).useLayoutEffect},26459:(e,t,r)=>{"use strict";var n=r(51609),s="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,a=n.useEffect,o=n.useLayoutEffect,c=n.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!s(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),s=n[0].inst,u=n[1];return o((function(){s.value=r,s.getSnapshot=t,l(s)&&u({inst:s})}),[e,r,t]),a((function(){return l(s)&&u({inst:s}),e((function(){l(s)&&u({inst:s})}))}),[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:u},43528:(e,t,r)=>{"use strict";var n=r(51609),s=r(94652),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=s.useSyncExternalStore,o=n.useRef,c=n.useEffect,l=n.useMemo,u=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,s){var d=o(null);if(null===d.current){var p={hasValue:!1,value:null};d.current=p}else p=d.current;d=l((function(){function e(e){if(!c){if(c=!0,a=e,e=n(e),void 0!==s&&p.hasValue){var t=p.value;if(s(t,e))return o=t}return o=e}if(t=o,i(a,e))return t;var r=n(e);return void 0!==s&&s(t,r)?t:(a=e,o=r)}var a,o,c=!1,l=void 0===r?null:r;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]}),[t,r,n,s]);var f=a(e,d[0],d[1]);return c((function(){p.hasValue=!0,p.value=f}),[f]),u(f),f}},94652:(e,t,r)=>{"use strict";e.exports=r(26459)},74590:(e,t,r)=>{"use strict";e.exports=r(43528)}}]);
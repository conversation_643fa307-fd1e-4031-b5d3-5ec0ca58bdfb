"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[9680],{59255:(e,c,n)=>{n.d(c,{W:()=>a});var t=n(56427),o=n(39793);const a=({size:e="medium"})=>(0,o.jsx)("div",{className:`woocommerce-field-placeholder woocommerce-field-placeholder--${e}`,children:(0,o.jsx)(t.Placeholder,{})})},32905:(e,c,n)=>{n.d(c,{w:()=>s});var t=n(56427),o=n(51609),a=n(39793);const s=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});s.Layout=({children:e})=>((0,o.useEffect)((()=>{const e=document.getElementById("wpbody");e&&e.querySelector(".settings-layout")&&e.classList.add("has-settings-layout")}),[]),(0,a.jsx)("div",{className:"settings-layout",children:e})),s.Section=({title:e,description:c,children:n,id:t})=>(0,a.jsxs)("div",{className:"settings-section",id:t,children:[(0,a.jsxs)("div",{className:"settings-section__details",children:[(0,a.jsx)("h2",{children:e}),(0,a.jsx)("p",{children:c})]}),(0,a.jsx)("div",{className:"settings-section__controls",children:n})]}),s.Actions=({children:e})=>(0,a.jsx)(t.Card,{className:"settings-card__wrapper ",children:(0,a.jsx)(t.CardBody,{className:"form__actions",children:e})}),s.Form=({children:e,onSubmit:c})=>(0,a.jsx)("form",{onSubmit:c,className:"settings-form",children:e})},53490:(e,c,n)=>{n.r(c),n.d(c,{SettingsPaymentsBacs:()=>k,default:()=>v});var t=n(27723),o=n(47143),a=n(56427),s=n(86087),i=n(40314),r=n(59255),l=n(98846),d=n(51609),m=n(18537);const u=e=>{switch(e){case"AU":return(0,t.__)("BSB","woocommerce");case"CA":return(0,t.__)("Bank transit number","woocommerce");case"IN":return(0,t.__)("IFSC","woocommerce");case"IT":return(0,t.__)("Branch sort","woocommerce");case"NZ":case"SE":return(0,t.__)("Bank code","woocommerce");case"US":return(0,t.__)("Routing number","woocommerce");case"ZA":return(0,t.__)("Branch code","woocommerce");default:return(0,t.__)("Sort code","woocommerce")}},_=e=>{switch(e){case"AU":case"CA":case"IN":case"IT":case"NZ":case"SE":case"US":case"ZA":case"GB":case"IE":return!0;default:return!1}},h=()=>Math.random().toString(36).substring(2,10),b=e=>""===e.trim()?"This field is required.":void 0;var x=n(39793);const p=({account:e,onClose:c,onSave:n,defaultCountry:o})=>{const s=window.wcSettings.countries,[i,r]=(0,d.useState)(e||{account_name:"",account_number:"",bank_name:"",sort_code:"",iban:"",bic:"",country_code:o}),[l,h]=(0,d.useState)(e?.country_code||o),[p,w]=(0,d.useState)({}),j=(e,c)=>{r((n=>({...n,[e]:c})))};return(0,x.jsxs)(a.Modal,{className:"bank-account-modal",title:e?(0,t.__)("Edit bank account","woocommerce"):(0,t.__)("Add a bank account","woocommerce"),onRequestClose:c,shouldCloseOnClickOutside:!1,children:[(0,x.jsxs)("div",{className:"bank-account-modal__content",children:[(0,x.jsx)("p",{className:"bank-account-modal__description",children:e?(0,t.__)("Edit your bank account details.","woocommerce"):(0,t.__)("Add your bank account details.","woocommerce")}),(0,x.jsx)(a.SelectControl,{className:"bank-account-modal__field is-required",label:(0,t.__)("Country","woocommerce"),required:!0,value:l,options:Object.entries(s).map((([e,c])=>({label:(0,m.decodeEntities)(c),value:e}))),onChange:e=>{h(e),j("country_code",e),j("sort_code","")}}),(0,x.jsx)(a.TextControl,{className:"bank-account-modal__field is-required",label:(0,t.__)("Account Name","woocommerce"),required:!0,value:i.account_name,onChange:e=>j("account_name",e),help:p.account_name?(0,x.jsx)("span",{className:"bank-account-modal__error",children:p.account_name}):void 0}),(0,x.jsx)(a.TextControl,{className:"bank-account-modal__field",label:(0,t.__)("Bank Name","woocommerce"),value:i.bank_name,onChange:e=>j("bank_name",e)}),(0,x.jsx)(a.TextControl,{className:"bank-account-modal__field is-required",label:(0,t.__)("Account Number","woocommerce"),required:!0,value:i.account_number,onChange:e=>j("account_number",e),help:p.account_number?(0,x.jsx)("span",{className:"bank-account-modal__error",children:p.account_number}):void 0}),_(l)&&(0,x.jsx)(a.TextControl,{className:"bank-account-modal__field is-required",label:u(l),required:!0,value:(g=i.sort_code||"",k=l,"GB"!==k&&"IE"!==k?g:null!==(v=g.replace(/\D/g,"").substring(0,6).match(/.{1,2}/g)?.join("-"))&&void 0!==v?v:""),onChange:e=>{"GB"!==l&&"IE"!==l||(e=e.replace(/\D/g,"").substring(0,6)),j("sort_code",e)},help:p.sort_code?(0,x.jsx)("span",{className:"bank-account-modal__error",children:p.sort_code}):void 0}),(0,x.jsx)(a.TextControl,{className:"bank-account-modal__field",label:(0,t.__)("IBAN","woocommerce"),value:i.iban,onChange:e=>j("iban",e),help:p.iban?(0,x.jsx)("span",{className:"bank-account-modal__error",children:p.iban}):void 0}),(0,x.jsx)(a.TextControl,{className:"bank-account-modal__field",label:(0,t.__)("BIC / SWIFT","woocommerce"),value:i.bic,onChange:e=>j("bic",e),help:p.bic?(0,x.jsx)("span",{className:"bank-account-modal__error",children:p.bic}):void 0})]}),(0,x.jsxs)("div",{className:"bank-account-modal__actions",children:[(0,x.jsx)(a.Button,{variant:"tertiary",onClick:c,children:(0,t.__)("Cancel","woocommerce")}),(0,x.jsx)(a.Button,{className:"bank-account-modal__save",variant:"primary",onClick:()=>{(()=>{const e={};var c;e.account_name=b(i.account_name),e.account_number=b(i.account_number)||(c=i.account_number,/^\d+$/.test(c.trim())?void 0:"This field must be numeric."),_(l)&&(e.sort_code=b(i.sort_code));const n=Object.fromEntries(Object.entries(e).filter((([,e])=>e)));return w(n),0===Object.keys(n).length})()&&n(i)},children:(0,t.__)("Save","woocommerce")})]})]});var g,k,v};var w=n(15698);const j=({accounts:e,onChange:c,defaultCountry:n})=>{const[o,s]=(0,d.useState)((()=>e.map((e=>({...e,id:h()})))));(0,d.useEffect)((()=>{e.length&&0===o.length&&s(e.map((e=>({...e,id:h()}))))}),[e,o.length]);const[i,r]=(0,d.useState)(null),[m,u]=(0,d.useState)(!1),[_,b]=(0,d.useState)(null),j=(e=null)=>{r(e),u(!0)};return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsxs)(w.q6,{items:o,className:"bank-accounts__list",setItems:e=>{s(e),c(e.map((({id:e,...c})=>c)))},children:[(0,x.jsx)("div",{className:"bank-accounts__list-header",children:(0,x.jsxs)("div",{className:"bank-accounts__list-item-inner",children:[(0,x.jsx)("div",{className:"bank-accounts__list-item-before"}),(0,x.jsxs)("div",{className:"bank-accounts__list-item-text",children:[(0,x.jsx)("div",{children:"Account Name"}),(0,x.jsx)("div",{children:"Account Number"}),(0,x.jsx)("div",{children:"Bank Name"})]}),(0,x.jsx)("div",{className:"bank-accounts__list-item-after"})]})}),o.map(((e,c)=>(0,x.jsx)(w.Uq,{id:e.id,className:"bank-accounts__list-item"+(0===c?" first-item":""),children:(0,x.jsxs)("div",{className:"bank-accounts__list-item-inner",children:[(0,x.jsx)("div",{className:"bank-accounts__list-item-before",children:(0,x.jsx)(w.Gh,{})}),(0,x.jsxs)("div",{className:"bank-accounts__list-item-text",children:[(0,x.jsx)("div",{children:e.account_name}),(0,x.jsx)("div",{children:e.account_number}),(0,x.jsx)("div",{children:e.bank_name})]}),(0,x.jsx)("div",{className:"bank-accounts__list-item-after",children:(0,x.jsx)(l.EllipsisMenu,{label:(0,t.__)("Options","woocommerce"),placement:"bottom-right",renderContent:({onClose:c=()=>{}})=>(0,x.jsxs)(a.MenuGroup,{children:[(0,x.jsx)(a.MenuItem,{role:"menuitem",onClick:()=>{c(),j(e)},children:(0,t.__)("View / edit","woocommerce")}),(0,x.jsx)(a.MenuItem,{isDestructive:!0,onClick:()=>{c(),b(e)},children:(0,t.__)("Delete","woocommerce")})]})})})]})},e.id))),(0,x.jsx)("li",{className:"bank-accounts__list-item action"+(0===o.length?" first-item":""),children:(0,x.jsx)(a.Button,{variant:"secondary",onClick:()=>j(null),children:(0,t.__)("+ Add account","woocommerce")})})]}),m&&(0,x.jsx)(p,{account:i,onClose:()=>u(!1),onSave:e=>{const n=o.findIndex((e=>e.id===i?.id));let t;-1!==n?(t=[...o],t[n]={...e,id:i?.id||h()}):t=[...o,{...e,id:h()}],s(t),c(t.map((({id:e,...c})=>c))),u(!1)},defaultCountry:n}),_&&(0,x.jsxs)(a.Modal,{title:(0,t.__)("Delete account","woocommerce"),onRequestClose:()=>b(null),shouldCloseOnClickOutside:!1,children:[(0,x.jsx)("p",{children:(0,t.__)("Are you sure you want to delete this bank account?","woocommerce")}),(0,x.jsxs)("div",{style:{display:"flex",justifyContent:"flex-end",gap:"8px",marginTop:"16px"},children:[(0,x.jsx)(a.Button,{variant:"secondary",onClick:()=>b(null),children:(0,t.__)("Cancel","woocommerce")}),(0,x.jsx)(a.Button,{variant:"primary",isDestructive:!0,onClick:()=>{if(!_)return;const e=o.filter((e=>e.id!==_.id));s(e),c(e.map((({id:e,...c})=>c))),b(null)},children:(0,t.__)("Delete","woocommerce")})]})]})]})};var g=n(32905);const k=()=>{const e=window.wcSettings?.admin?.preloadSettings?.general?.woocommerce_default_country||"US",{createSuccessNotice:c,createErrorNotice:n}=(0,o.useDispatch)("core/notices"),{bacsSettings:l,isLoading:d}=(0,o.useSelect)((e=>({bacsSettings:e(i.paymentGatewaysStore).getPaymentGateway("bacs"),isLoading:!e(i.paymentGatewaysStore).hasFinishedResolution("getPaymentGateway",["bacs"])})),[]),{accountsOption:m,isLoadingAccounts:u}=(0,o.useSelect)((e=>{const c=e(i.optionsStore);return{accountsOption:c.getOption("woocommerce_bacs_accounts"),isLoadingAccounts:!c.hasFinishedResolution("getOption",["woocommerce_bacs_accounts"])}}),[]),[_,h]=(0,s.useState)({}),[b,p]=(0,s.useState)(!1),[w,k]=(0,s.useState)(!1);(0,s.useEffect)((()=>{l&&(h({enabled:l.enabled,title:l.settings.title.value,description:l.description,instructions:l.settings.instructions.value}),k(!1))}),[l]);const[v,y]=(0,s.useState)([]);(0,s.useEffect)((()=>{m&&y(m)}),[m]);const{updateOptions:C}=(0,o.useDispatch)(i.optionsStore),{updatePaymentGateway:S}=(0,o.useDispatch)(i.paymentGatewaysStore);return(0,x.jsx)(g.w,{children:(0,x.jsx)(g.w.Layout,{children:(0,x.jsxs)(g.w.Form,{onSubmit:e=>{e.preventDefault(),(async()=>{if(!l)return;p(!0);const e={title:String(_.title),instructions:String(_.instructions)};try{await Promise.all([C({woocommerce_bacs_accounts:v.map((({account_name:e,account_number:c,bank_name:n,sort_code:t,iban:o,bic:a,country_code:s})=>({account_name:e,account_number:c,bank_name:n,sort_code:t,iban:o,bic:a,country_code:s})))}),S("bacs",{enabled:Boolean(_.enabled),description:String(_.description),settings:e})]),c((0,t.__)("Settings updated successfully","woocommerce"))}catch(e){n((0,t.__)("Failed to update settings","woocommerce"))}finally{p(!1),k(!1)}})()},children:[(0,x.jsxs)(g.w.Section,{title:(0,t.__)("Enable and customise","woocommerce"),description:(0,t.__)("Choose how you want to present bank transfer to your customers during checkout.","woocommerce"),children:[d?(0,x.jsx)(r.W,{size:"small"}):(0,x.jsx)(a.CheckboxControl,{label:(0,t.__)("Enable direct bank transfers","woocommerce"),checked:Boolean(_.enabled),onChange:e=>{h({..._,enabled:e}),k(!0)}}),d?(0,x.jsx)(r.W,{size:"medium"}):(0,x.jsx)(a.TextControl,{label:(0,t.__)("Title","woocommerce"),help:(0,t.__)("Payment method name that the customer will see during checkout.","woocommerce"),placeholder:(0,t.__)("Direct bank transfer payments","woocommerce"),value:String(_.title),onChange:e=>{h({..._,title:e}),k(!0)}}),d?(0,x.jsx)(r.W,{size:"large"}):(0,x.jsx)(a.TextareaControl,{label:(0,t.__)("Description","woocommerce"),help:(0,t.__)("Payment method description that the customer will see during checkout.","woocommerce"),value:String(_.description),onChange:e=>{h({..._,description:e}),k(!0)}}),d?(0,x.jsx)(r.W,{size:"large"}):(0,x.jsx)(a.TextareaControl,{label:(0,t.__)("Instructions","woocommerce"),help:(0,t.__)("Instructions that will be added to the thank you page and emails.","woocommerce"),value:String(_.instructions),onChange:e=>{h({..._,instructions:e}),k(!0)}})]}),(0,x.jsx)(g.w.Section,{title:(0,t.__)("Account details","woocommerce"),description:(0,t.__)("Configure your bank account details.","woocommerce"),children:u?(0,x.jsx)(r.W,{size:"large"}):(0,x.jsx)(j,{accounts:v,onChange:e=>{y(e),k(!0)},defaultCountry:e})}),(0,x.jsx)(g.w.Actions,{children:(0,x.jsx)(a.Button,{variant:"primary",type:"submit",isBusy:b,disabled:b||!w,children:(0,t.__)("Save changes","woocommerce")})})]})})})},v=k}}]);
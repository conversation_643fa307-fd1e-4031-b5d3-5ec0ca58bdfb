"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[5113],{4584:(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var r=o(86087),s=o(27723),a=o(52619);const c=(0,a.applyFilters)("woocommerce_admin_stock_report_advanced_filters",{filters:{},title:(0,s._x)("Products Match <select/> Filters","A sentence describing filters for Products. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")}),l=(0,a.applyFilters)("woocommerce_admin_stock_report_filters",[{label:(0,s.__)("Show","woocommerce"),staticParams:["paged","per_page"],param:"type",showFilters:()=>!0,filters:[{label:(0,s.__)("All products","woocommerce"),value:"all"},{label:(0,s.__)("Out of stock","woocommerce"),value:"outofstock"},{label:(0,s.__)("Low stock","woocommerce"),value:"lowstock"},{label:(0,s.__)("In stock","woocommerce"),value:"instock"},{label:(0,s.__)("On backorder","woocommerce"),value:"onbackorder"}]},{label:(0,s.__)("Filter by","woocommerce"),staticParams:["paged","per_page"],param:"filter",showFilters:()=>Object.keys(c.filters).length,filters:[{label:(0,s.__)("All Products","woocommerce"),value:"all"},{label:(0,s.__)("Advanced Filters","woocommerce"),value:"advanced"}]}]);var n=o(18537),i=o(98846),m=o(96476),u=o(43577),d=o(15703),_=o(94111),p=o(97605),k=o(56109),w=o(39793);const b=(0,k.Qk)("stockStatuses",{});class y extends r.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,s.__)("Product / Variation","woocommerce"),key:"title",required:!0,isLeftAligned:!0,isSortable:!0},{label:(0,s.__)("SKU","woocommerce"),key:"sku",isSortable:!0},{label:(0,s.__)("Status","woocommerce"),key:"stock_status",isSortable:!0,defaultSort:!0},{label:(0,s.__)("Stock","woocommerce"),key:"stock_quantity",isSortable:!0}]}getRowsContent(e=[]){const{query:t}=this.props,o=(0,m.getPersistedQuery)(t);return e.map((e=>{const{id:t,manage_stock:r,parent_id:a,sku:c,stock_quantity:l,stock_status:_,low_stock_amount:p}=e,k=(0,n.decodeEntities)(e.name),y=(0,m.getNewPath)(o,"/analytics/products",{filter:"single_product",products:a||t}),h=(0,w.jsx)(i.Link,{href:y,type:"wc-admin",children:k}),f=(0,d.getAdminLink)("post.php?action=edit&post="+(a||t));var g,v,C;return[{display:h,value:k},{display:c,value:c},{display:(g=_,C=p,(v=l)&&g&&v<=C==="instock"?(0,w.jsx)(i.Link,{href:f,type:"wp-admin",children:(0,s._x)("Low","Indication of a low quantity","woocommerce")}):(0,w.jsx)(i.Link,{href:f,type:"wp-admin",children:b[_]})),value:b[_]},{display:r?(0,u.formatValue)(this.context.getCurrencyConfig(),"number",l):(0,s.__)("N/A","woocommerce"),value:l}]}))}getSummary(e){const{products:t=0,outofstock:o=0,lowstock:r=0,instock:a=0,onbackorder:c=0}=e,l=this.context.getCurrencyConfig();return[{label:(0,s._n)("Product","Products",t,"woocommerce"),value:(0,u.formatValue)(l,"number",t)},{label:(0,s.__)("Out of stock","woocommerce"),value:(0,u.formatValue)(l,"number",o)},{label:(0,s.__)("Low stock","woocommerce"),value:(0,u.formatValue)(l,"number",r)},{label:(0,s.__)("On backorder","woocommerce"),value:(0,u.formatValue)(l,"number",c)},{label:(0,s.__)("In stock","woocommerce"),value:(0,u.formatValue)(l,"number",a)}]}render(){const{advancedFilters:e,filters:t,query:o}=this.props;return(0,w.jsx)(p.A,{endpoint:"stock",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,getSummary:this.getSummary,summaryFields:["products","outofstock","lowstock","instock","onbackorder"],query:o,tableQuery:{orderby:o.orderby||"stock_status",order:o.order||"asc",type:o.type||"all"},title:(0,s.__)("Stock","woocommerce"),filters:t,advancedFilters:e})}}y.contextType=_.CurrencyContext;const h=y;var f=o(88711);class g extends r.Component{render(){const{query:e,path:t}=this.props;return(0,w.jsxs)(r.Fragment,{children:[(0,w.jsx)(f.A,{query:e,path:t,showDatePicker:!1,filters:l,advancedFilters:c,report:"stock"}),(0,w.jsx)(h,{query:e,filters:l,advancedFilters:c})]})}}}}]);
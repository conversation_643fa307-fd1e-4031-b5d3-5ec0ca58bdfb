"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[3040],{37148:(e,t,o)=>{o.d(t,{a:()=>s});var n=o(17697),a=o.n(n),i=o(86087),r=o(23772);const s=({children:e,className:t})=>(0,i.createElement)("div",{className:a()("woocommerce-onboarding-loader",t)},e);s.Layout=({children:e,className:t})=>(0,i.createElement)("div",{className:a()("woocommerce-onboarding-loader-wrapper",t)},(0,i.createElement)("div",{className:a()("woocommerce-onboarding-loader-container",t)},e)),s.Illustration=({children:e})=>(0,i.createElement)(i.Fragment,null,e),s.Title=({children:e,className:t})=>(0,i.createElement)("h1",{className:a()("woocommerce-onboarding-loader__title",t)},e),s.ProgressBar=({progress:e,className:t})=>(0,i.createElement)(r.A,{className:a()("progress-bar",t),percent:null!=e?e:0,color:"var(--wp-admin-theme-color)",bgcolor:"#E0E0E0"}),s.Subtext=({children:e,className:t})=>(0,i.createElement)("p",{className:a()("woocommerce-onboarding-loader__paragraph",t)},e),s.Sequence=({interval:e,shouldLoop:t=!0,children:o,onChange:n=()=>{}})=>{const[a,r]=(0,i.useState)(0),s=i.Children.count(o);(0,i.useEffect)((()=>{const o=setInterval((()=>{r((e=>{const a=e+1;if(t){const e=a%s;return n(e),e}return a<s?(n(a),a):(clearInterval(o),e)}))}),e);return()=>clearInterval(o)}),[e,o,t,s]);const c=i.Children.toArray(o)[a];return(0,i.createElement)(i.Fragment,null,c)}},14695:(e,t,o)=>{o.d(t,{L:()=>j});var n=o(51609),a=o(86087),i=o(56427),r=o(18015),s=o(12486),c=o(27193),l=o(6457),m=o(94084),d=o(90423),h=o(31183),u=o(76147),p=o(22056),w=o(96043),y=o(70145),f=o(61727),g=o(97021),v=o(27707),b=o(30487),_=o(97723),x=o(80019),E=o(66012),k=o(17479),A=o(93342),z=o(10630);const N=[{name:"visa",component:(0,a.createElement)(r.A,{key:"visa"})},{name:"mastercard",component:(0,a.createElement)(s.A,{key:"mastercard"})},{name:"amex",component:(0,a.createElement)(c.A,{key:"amex"})},{name:"discover",component:(0,a.createElement)(l.A,{key:"discover"})},{name:"woopay",component:(0,a.createElement)(u.A,{key:"woopay"})},{name:"applepay",component:(0,a.createElement)(m.A,{key:"applepay"})},{name:"googlepay",component:(0,a.createElement)(d.A,{key:"googlepay"})},{name:"afterpay",component:(0,a.createElement)(p.A,{key:"afterpay"})},{name:"affirm",component:(0,a.createElement)(w.A,{key:"affirm"})},{name:"klarna",component:(0,a.createElement)(y.A,{key:"klarna"})},{name:"cartebancaire",component:(0,a.createElement)(f.A,{key:"cartebancaire"})},{name:"unionpay",component:(0,a.createElement)(g.A,{key:"unionpay"})},{name:"diners",component:(0,a.createElement)(v.A,{key:"diners"})},{name:"eftpos",component:(0,a.createElement)(b.A,{key:"eftpos"})},{name:"jcb",component:(0,a.createElement)(h.A,{key:"jcb"})},{name:"bancontact",component:(0,a.createElement)(x.A,{key:"bancontact"})},{name:"becs",component:(0,a.createElement)(k.A,{key:"becs"})},{name:"eps",component:(0,a.createElement)(E.A,{key:"eps"})},{name:"ideal",component:(0,a.createElement)(_.A,{key:"ideal"})},{name:"przelewy24",component:(0,a.createElement)(A.A,{key:"przelewy24"})},{name:"grabpay",component:(0,a.createElement)(z.A,{key:"grabpay"})}],j=({isWooPayEligible:e=!1,maxElements:t=10,tabletWidthBreakpoint:o=768,maxElementsTablet:r=7,mobileWidthBreakpoint:s=480,maxElementsMobile:c=5,totalPaymentMethods:l=21})=>{const[m,d]=(0,n.useState)(t),[h,u]=(0,n.useState)(!1),p=(0,n.useRef)(null),w=e=>{const t=e.target.closest(".woocommerce-woopayments-payment-methods-logos-count");p.current&&t!==p.current||u((e=>!e))},y=e?l:l-1,f=t=>e?t:t+1;(0,n.useEffect)((()=>{const e=()=>{window.innerWidth<=s?d(c):window.innerWidth<=o?d(r):d(t)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[t,c,r,o,s]);const g=N.slice(0,f(m)).filter((t=>e||"woopay"!==t.name)),v=N.slice(f(m)).filter((t=>e||"woopay"!==t.name));return(0,a.createElement)("div",{className:"woocommerce-woopayments-payment-methods-logos"},g.map((e=>e.component)),m<y&&(0,a.createElement)("div",{className:"woocommerce-woopayments-payment-methods-logos-count",role:"button",tabIndex:0,ref:p,onClick:w,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||w(e)}},"+ ",y-m,h&&(0,a.createElement)(i.Popover,{className:"woocommerce-woopayments-payment-methods-logos-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{u(!1)}},(0,a.createElement)("div",{className:"woocommerce-woopayments-payment-methods-logos"},v.map((e=>e.component))))))}},29568:(e,t,o)=>{o.d(t,{b:()=>s,J:()=>c});var n=o(84343),a=o.n(n),i=o(27723);const r={PH:{"National Capital Region":(0,i.__)("Metro Manila","woocommerce")},IT:{Rome:(0,i.__)("Roma","woocommerce")}},s=(e,t,o=.7)=>{if(!t)return null;let n=null,i=o;const s=(({country_short:e,region:t="",city:o=""})=>{if(!e)return null;const n=r[e];if(!n)return null;const a=n[t];return a||(n[o]||null)})(t);for(const o of e){if(o.key===t.country_short)return o;if(o.key.split(":")[0]===t.country_short&&o.label.includes("—")){const e=o.label.split("—")[1].trim();if(s===e)return o;if(0===e.localeCompare(t.region||"","en",{sensitivity:"base"})||0===e.localeCompare(t.city||"","en",{sensitivity:"base"}))return o;const r=Math.max(a().compareTwoStrings(e,t.region||""),a().compareTwoStrings(e,t.city||""));r>=i&&(n=o,i=r)}}return n},c=e=>{var t;return null!==(t=e?.split(":")[0])&&void 0!==t?t:void 0}},61208:(e,t,o)=>{o.d(t,{A:()=>w,n:()=>p});var n=o(4921),a=o(86087),i=o(56427),r=o(29491),s=o(27723),c=o(39793);const l={position:void 0,userSelect:void 0,cursor:void 0,width:void 0,height:void 0,top:void 0,right:void 0,bottom:void 0,left:void 0},m=320,d=9/19.5,h={width:"100%",height:"100%"};function u(e,t){const o=1-Math.max(0,Math.min(1,(e-m)/980));var n;return e/((n=t)+(d-n)*o)}const p=(0,a.createContext)(!1),w=function e({isFullWidth:t,isOversized:o,setIsOversized:d,isReady:w,children:y,defaultSize:f,innerContentStyle:g,isHandleVisibleByDefault:v=!1}){const[b,_]=(0,a.useState)(h),[x,E]=(0,a.useState)(),[k,A]=(0,a.useState)(!1),[z,N]=(0,a.useState)(!1),[j,M]=(0,a.useState)(1),S={type:"tween",duration:k?0:.5},[C,R]=(0,a.useState)(!1),W=(0,a.useRef)(null),L=(0,r.useInstanceId)(e,"woocommerce-edit-site-resizable-frame-handle-help"),P=f.width/f.height,T={default:{flexGrow:0,height:b.height},fullWidth:{flexGrow:1,height:b.height}},B=k||v?"active":z?"visible":"hidden",I=(0,c.jsx)(i.__unstableMotion.button,{role:"separator","aria-orientation":"vertical",className:(0,n.A)("woocommerce-edit-site-resizable-frame__handle",{"is-resizing":k}),variants:{hidden:{opacity:0,left:0},visible:{opacity:.6,left:-10},active:{opacity:1,left:-10}},animate:B,"aria-label":(0,s.__)("Drag to resize","woocommerce"),"aria-describedby":L,"aria-valuenow":W.current?.resizable?.offsetWidth||void 0,"aria-valuemin":m,"aria-valuemax":f.width,onKeyDown:e=>{if(!["ArrowLeft","ArrowRight"].includes(e.key))return;e.preventDefault();const t=20*(e.shiftKey?5:1)*("ArrowLeft"===e.key?1:-1),o=Math.min(Math.max(m,W.current.resizable.offsetWidth+t),f.width);_({width:o,height:u(o,P)})},initial:"hidden",exit:"hidden",whileFocus:"active",whileHover:"active",children:v&&!C&&(0,c.jsx)(i.Popover,{className:"woocommerce-assembler-hub__resizable-frame__drag-handler",position:"middle right",children:(0,s.__)("Drag to resize","woocommerce")})},"handle");return(0,c.jsx)(i.ResizableBox,{as:i.__unstableMotion.div,ref:W,initial:!1,variants:T,animate:t?"fullWidth":"default",onAnimationComplete:e=>{"fullWidth"===e&&_({width:"100%",height:"100%"})},transition:S,size:b,enable:{top:!1,right:!1,bottom:!1,left:w,topRight:!1,bottomRight:!1,bottomLeft:!1,topLeft:!1},resizeRatio:j,handleClasses:void 0,handleStyles:{left:l,right:l},minWidth:m,maxWidth:"100%",maxHeight:"100%",onFocus:()=>N(!0),onBlur:()=>N(!1),onMouseOver:()=>N(!0),onMouseOut:()=>N(!1),handleComponent:{left:(0,c.jsxs)(c.Fragment,{children:[v?(0,c.jsx)("div",{children:I}):(0,c.jsx)(i.Tooltip,{position:"middle right",text:(0,s.__)("Drag to resize","woocommerce"),children:I}),(0,c.jsx)("div",{hidden:!0,id:L,children:(0,s.__)("Use left and right arrow keys to resize the canvas. Hold shift to resize in larger increments.","woocommerce")})]})},onResizeStart:(e,t,o)=>{E(o.offsetWidth),A(!0)},onResize:(e,t,n,a)=>{const i=a.width/j,r=Math.abs(i),s=a.width<0?r:(f.width-x)/2,c=Math.min(r,s),l=0===r?0:c/r;M(1-l+2*l);const m=x+a.width;d(m>f.width),_({height:o?"100%":u(m,P)})},onResizeStop:(e,t,n)=>{A(!1),C||R(!0),o&&(d(!1),_(h))},className:(0,n.A)("woocommerce-edit-site-resizable-frame__inner",{"is-resizing":k}),children:(0,c.jsx)(i.__unstableMotion.div,{className:"customize-your-store-edit-site-resizable-frame__inner-content",animate:{borderRadius:t?0:8},transition:S,style:g,children:(0,c.jsx)(p.Provider,{value:k,children:y})})})}},64155:(e,t,o)=>{o.d(t,{b:()=>y});var n=o(4921),a=o(47143),i=o(56427),r=o(29491),s=o(3582),c=o(18537),l=o(86087),m=o(23522),d=o(96476),h=o(98846),u=o(39793);const p=function({className:e="wordpress-logo",size:t=72}){return(0,u.jsx)("svg",{className:e,height:t,width:t,viewBox:"0 0 72 72",children:(0,u.jsx)("path",{d:"M36,0C16.1,0,0,16.1,0,36c0,19.9,16.1,36,36,36c19.9,0,36-16.2,36-36C72,16.1,55.8,0,36,0z M3.6,36 c0-4.7,1-9.1,2.8-13.2l15.4,42.3C11.1,59.9,3.6,48.8,3.6,36z M36,68.4c-3.2,0-6.2-0.5-9.1-1.3l9.7-28.2l9.9,27.3 c0.1,0.2,0.1,0.3,0.2,0.4C43.4,67.7,39.8,68.4,36,68.4z M40.5,20.8c1.9-0.1,3.7-0.3,3.7-0.3c1.7-0.2,1.5-2.8-0.2-2.7 c0,0-5.2,0.4-8.6,0.4c-3.2,0-8.5-0.4-8.5-0.4c-1.7-0.1-2,2.6-0.2,2.7c0,0,1.7,0.2,3.4,0.3l5,13.8L28,55.9L16.2,20.8 c2-0.1,3.7-0.3,3.7-0.3c1.7-0.2,1.5-2.8-0.2-2.7c0,0-5.2,0.4-8.6,0.4c-0.6,0-1.3,0-2.1,0C14.7,9.4,24.7,3.6,36,3.6 c8.4,0,16.1,3.2,21.9,8.5c-0.1,0-0.3,0-0.4,0c-3.2,0-5.4,2.8-5.4,5.7c0,2.7,1.5,4.9,3.2,7.6c1.2,2.2,2.7,4.9,2.7,8.9 c0,2.8-0.8,6.3-2.5,10.5l-3.2,10.8L40.5,20.8z M52.3,64l9.9-28.6c1.8-4.6,2.5-8.3,2.5-11.6c0-1.2-0.1-2.3-0.2-3.3 c2.5,4.6,4,9.9,4,15.5C68.4,47.9,61.9,58.4,52.3,64z"})})};var w=o(38966);const y=(0,l.forwardRef)((({isTransparent:e,...t},o)=>{const{siteTitle:l}=(0,a.useSelect)((e=>{const{getSite:t}=e(s.store);return{siteTitle:t()?.title}}),[]),y=(0,r.useReducedMotion)();return(0,u.jsx)(i.__unstableMotion.div,{ref:o,...t,className:(0,n.A)("woocommerce-edit-site-site-hub",t.className),initial:!1,transition:{type:"tween",duration:y?0:.3,ease:"easeOut"},children:(0,u.jsx)(i.__experimentalHStack,{justify:"space-between",alignment:"center",className:"woocommerce-edit-site-site-hub__container",children:(0,u.jsxs)(i.__experimentalHStack,{justify:"flex-start",className:"woocommerce-edit-site-site-hub__text-content",spacing:"0",children:[(0,u.jsx)("div",{className:(0,n.A)("woocommerce-edit-site-site-hub__view-mode-toggle-container",{"has-transparent-background":e}),children:(0,u.jsx)(h.Link,{href:(0,d.getNewPath)((0,d.getPersistedQuery)(),"/",{}),type:"wp-admin",children:(0,w.ex)()?(0,u.jsx)(p,{size:24,className:"woocommerce-cys-wordpress-header-logo"}):(0,u.jsx)(m.A,{className:"woocommerce-edit-site-layout__view-mode-toggle-icon"})})}),!(0,w.ex)()&&(0,u.jsx)(i.__unstableAnimatePresence,{children:(0,u.jsx)(i.__unstableMotion.div,{layout:!1,animate:{opacity:1},exit:{opacity:0},className:(0,n.A)("woocommerce-edit-site-site-hub__site-title",{"is-transparent":e}),transition:{type:"tween",duration:y?0:.2,ease:"easeOut",delay:.1},children:(0,c.decodeEntities)(l)})})]})})})}))},38966:(e,t,o)=>{o.d(t,{ex:()=>i,ot:()=>a});var n=o(52619);const a="entrepreneur-signup";(0,n.addFilter)("woocommerce_admin_persisted_queries","woocommerce_admin_customize_your_store",(e=>(e.push("ref"),e)));const i=()=>new URLSearchParams(window.location.search).get("ref")===a},97687:(e,t,o)=>{o.d(t,{E:()=>n});const n=()=>void 0!==window.wcCalypsoBridge&&window.wcCalypsoBridge.isWooExpress},42843:(e,t,o)=>{function n(e,t=new Set){if(!t.has(e)){t.add(e);for(const o in e)if(e.hasOwnProperty(o)){if("component"===o)return e;if("object"==typeof e[o]&&null!==e[o]){const a=n(e[o],t);if(void 0!==a)return a}}}}o.d(t,{Q:()=>n})}}]);
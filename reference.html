<main id="content" class="site-main post-23165 page type-page status-publish hentry">

			<div class="page-header">
			<h1 class="entry-title">Checkout</h1>		</div>
	
	<div class="page-content">
		<div class="woocommerce"><div class="woocommerce-notices-wrapper"></div><div class="checkout-top-bar">
    <div class="checkout-top-bar-inner">
        <div class="top-bar-left">
            <a href="https://criminal.hyroes.com" class="back-arrow">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_154_506)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M3.41679 6.99996H14.9998C15.265 6.99996 15.5194 7.10532 15.7069 7.29285C15.8944 7.48039 15.9998 7.73474 15.9998 7.99996C15.9998 8.26518 15.8944 8.51953 15.7069 8.70707C15.5194 8.8946 15.265 8.99996 14.9998 8.99996H3.41679L8.00779 13.591C8.19556 13.7786 8.3011 14.0331 8.3012 14.2986C8.30129 14.5641 8.19593 14.8187 8.00829 15.0065C7.82065 15.1942 7.5661 15.2998 7.30064 15.2999C7.03518 15.3 6.78056 15.1946 6.59279 15.007L0.292786 8.70696C0.105315 8.51943 0 8.26512 0 7.99996C0 7.73479 0.105315 7.48049 0.292786 7.29296L6.59279 0.992959C6.78069 0.805318 7.03544 0.700008 7.30099 0.700196C7.56655 0.700383 7.82115 0.806053 8.00879 0.993959C8.19643 1.18186 8.30174 1.43661 8.30155 1.70217C8.30136 1.96772 8.19569 2.22232 8.00779 2.40996L3.41679 6.99996Z" fill="#8792A2"></path>
                    </g>
                    <defs>
                        <clipPath id="clip0_154_506">
                            <rect width="16" height="16" fill="white"></rect>
                        </clipPath>
                    </defs>
                </svg>
            </a>
            <div class="checkout-logo">
                <img decoding="async" src="https://criminal.hyroes.com/wp-content/themes/hello-elementor-child/assets/img/logo.svg" alt="Criminal Modz" width="164" data-src="https://criminal.hyroes.com/wp-content/themes/hello-elementor-child/assets/img/logo.svg" class=" lazyloaded"><noscript><img decoding="async" src="https://criminal.hyroes.com/wp-content/themes/hello-elementor-child/assets/img/logo.svg" alt="Criminal Modz" width="164" data-eio="l"></noscript>
            </div>
        </div>
        <div class="top-bar-right">
            <span class="dark-mode-text">Dark Mode</span>
            <div class="dark-mode-toggle">
                <svg width="70" height="42" viewBox="0 0 70 42" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#filter0_ddd_154_510)">
                        <rect x="5" y="3" width="60" height="32" rx="16" fill="url(#paint0_linear_154_510)"></rect>
                    </g>
                    <g filter="url(#filter1_dd_154_510)">
                        <circle cx="22" cy="19" r="12" fill="white"></circle>
                    </g>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M47.1652 13.1299C47.3408 13.2754 47.4181 13.514 47.3629 13.74C47.2562 14.1766 47.1994 14.6345 47.1994 15.1069C47.1994 18.1691 49.5923 20.6515 52.544 20.6515C53.4171 20.6515 54.2398 20.4348 54.9663 20.0508C55.1665 19.945 55.4079 19.9705 55.5836 20.116C55.7592 20.2615 55.8365 20.5001 55.7813 20.7261C55.0689 23.642 52.5231 25.8 49.4899 25.8C45.9056 25.8 43 22.7857 43 19.0673C43 16.4467 44.4434 14.1768 46.5479 13.0647C46.7481 12.9589 46.9895 12.9844 47.1652 13.1299Z" fill="white"></path>
                    <g filter="url(#filter2_ddd_154_510)">
                        <path d="M22.4 13C22.7314 13 23 13.2686 23 13.6V14.8C23 15.1314 22.7314 15.4 22.4 15.4C22.0686 15.4 21.8 15.1314 21.8 14.8V13.6C21.8 13.2686 22.0686 13 22.4 13Z" fill="url(#paint1_linear_154_510)"></path>
                        <path d="M22.4 23.4C22.7314 23.4 23 23.6686 23 24V25.2C23 25.5314 22.7314 25.8 22.4 25.8C22.0686 25.8 21.8 25.5314 21.8 25.2V24C21.8 23.6686 22.0686 23.4 22.4 23.4Z" fill="url(#paint2_linear_154_510)"></path>
                        <path d="M22.4 17C21.0745 17 20 18.0745 20 19.4C20 20.7255 21.0745 21.8 22.4 21.8C23.7255 21.8 24.8 20.7255 24.8 19.4C24.8 18.0745 23.7255 17 22.4 17Z" fill="url(#paint3_linear_154_510)"></path>
                        <path d="M26.9254 15.7231C27.1597 15.4888 27.1597 15.1089 26.9254 14.8746C26.6911 14.6402 26.3112 14.6402 26.0769 14.8746L25.2284 15.7231C24.9941 15.9574 24.9941 16.3373 25.2284 16.5716C25.4627 16.8059 25.8426 16.8059 26.0769 16.5716L26.9254 15.7231Z" fill="url(#paint4_linear_154_510)"></path>
                        <path d="M19.5715 23.077C19.8058 22.8427 19.8058 22.4628 19.5715 22.2285C19.3372 21.9942 18.9573 21.9942 18.723 22.2285L17.8745 23.077C17.6402 23.3113 17.6402 23.6912 17.8745 23.9255C18.1088 24.1598 18.4887 24.1598 18.723 23.9255L19.5715 23.077Z" fill="url(#paint5_linear_154_510)"></path>
                        <path d="M28.8 19.4C28.8 19.7314 28.5314 20 28.2 20H27C26.6686 20 26.4 19.7314 26.4 19.4C26.4 19.0686 26.6686 18.8 27 18.8H28.2C28.5314 18.8 28.8 19.0686 28.8 19.4Z" fill="url(#paint6_linear_154_510)"></path>
                        <path d="M18.4 19.4C18.4 19.7314 18.1314 20 17.8 20H16.6C16.2686 20 16 19.7314 16 19.4C16 19.0686 16.2686 18.8 16.6 18.8H17.8C18.1314 18.8 18.4 19.0686 18.4 19.4Z" fill="url(#paint7_linear_154_510)"></path>
                        <path d="M26.0768 23.9254C26.3111 24.1598 26.691 24.1598 26.9253 23.9254C27.1597 23.6911 27.1597 23.3112 26.9253 23.0769L26.0768 22.2284C25.8425 21.9941 25.4626 21.9941 25.2283 22.2284C24.994 22.4627 24.994 22.8426 25.2283 23.0769L26.0768 23.9254Z" fill="url(#paint8_linear_154_510)"></path>
                        <path d="M18.7229 16.5715C18.9572 16.8058 19.3371 16.8058 19.5714 16.5715C19.8058 16.3372 19.8057 15.9573 19.5714 15.723L18.7229 14.8745C18.4886 14.6402 18.1087 14.6402 17.8744 14.8745C17.6401 15.1088 17.6401 15.4887 17.8744 15.723L18.7229 16.5715Z" fill="url(#paint9_linear_154_510)"></path>
                    </g>
                    <defs>
                        <filter id="filter0_ddd_154_510" x="0" y="0" width="70" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                            <feOffset dy="1"></feOffset>
                            <feGaussianBlur stdDeviation="0.5"></feGaussianBlur>
                            <feComposite in2="hardAlpha" operator="out"></feComposite>
                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"></feColorMatrix>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_154_510"></feBlend>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                            <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect2_dropShadow_154_510"></feMorphology>
                            <feOffset></feOffset>
                            <feComposite in2="hardAlpha" operator="out"></feComposite>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.16 0"></feColorMatrix>
                            <feBlend mode="normal" in2="effect1_dropShadow_154_510" result="effect2_dropShadow_154_510"></feBlend>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                            <feOffset dy="2"></feOffset>
                            <feGaussianBlur stdDeviation="2.5"></feGaussianBlur>
                            <feComposite in2="hardAlpha" operator="out"></feComposite>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.08 0"></feColorMatrix>
                            <feBlend mode="normal" in2="effect2_dropShadow_154_510" result="effect3_dropShadow_154_510"></feBlend>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_154_510" result="shape"></feBlend>
                        </filter>
                        <filter id="filter1_dd_154_510" x="4" y="4" width="36" height="36" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                            <feOffset dy="3"></feOffset>
                            <feGaussianBlur stdDeviation="3"></feGaussianBlur>
                            <feComposite in2="hardAlpha" operator="out"></feComposite>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.08 0"></feColorMatrix>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_154_510"></feBlend>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                            <feOffset dy="1"></feOffset>
                            <feGaussianBlur stdDeviation="0.5"></feGaussianBlur>
                            <feComposite in2="hardAlpha" operator="out"></feComposite>
                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"></feColorMatrix>
                            <feBlend mode="normal" in2="effect1_dropShadow_154_510" result="effect2_dropShadow_154_510"></feBlend>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_154_510" result="shape"></feBlend>
                        </filter>
                        <filter id="filter2_ddd_154_510" x="11" y="10" width="22.8008" height="22.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                            <feOffset dy="1"></feOffset>
                            <feGaussianBlur stdDeviation="0.5"></feGaussianBlur>
                            <feComposite in2="hardAlpha" operator="out"></feComposite>
                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"></feColorMatrix>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_154_510"></feBlend>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                            <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect2_dropShadow_154_510"></feMorphology>
                            <feOffset></feOffset>
                            <feComposite in2="hardAlpha" operator="out"></feComposite>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.16 0"></feColorMatrix>
                            <feBlend mode="normal" in2="effect1_dropShadow_154_510" result="effect2_dropShadow_154_510"></feBlend>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                            <feOffset dy="2"></feOffset>
                            <feGaussianBlur stdDeviation="2.5"></feGaussianBlur>
                            <feComposite in2="hardAlpha" operator="out"></feComposite>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.08 0"></feColorMatrix>
                            <feBlend mode="normal" in2="effect3_dropShadow_154_510" result="effect3_dropShadow_154_510"></feBlend>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_154_510" result="shape"></feBlend>
                        </filter>
                        <linearGradient id="paint0_linear_154_510" x1="5" y1="19" x2="65" y2="19" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"></stop>
                            <stop offset="1" stop-color="#FF4747"></stop>
                        </linearGradient>
                        <linearGradient id="paint1_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"></stop>
                            <stop offset="1" stop-color="#FF4747"></stop>
                        </linearGradient>
                        <linearGradient id="paint2_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"></stop>
                            <stop offset="1" stop-color="#FF4747"></stop>
                        </linearGradient>
                        <linearGradient id="paint3_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"></stop>
                            <stop offset="1" stop-color="#FF4747"></stop>
                        </linearGradient>
                        <linearGradient id="paint4_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"></stop>
                            <stop offset="1" stop-color="#FF4747"></stop>
                        </linearGradient>
                        <linearGradient id="paint5_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"></stop>
                            <stop offset="1" stop-color="#FF4747"></stop>
                        </linearGradient>
                        <linearGradient id="paint6_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"></stop>
                            <stop offset="1" stop-color="#FF4747"></stop>
                        </linearGradient>
                        <linearGradient id="paint7_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"></stop>
                            <stop offset="1" stop-color="#FF4747"></stop>
                        </linearGradient>
                        <linearGradient id="paint8_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"></stop>
                            <stop offset="1" stop-color="#FF4747"></stop>
                        </linearGradient>
                        <linearGradient id="paint9_linear_154_510" x1="16" y1="19.4" x2="28.8" y2="19.4" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C52233"></stop>
                            <stop offset="1" stop-color="#FF4747"></stop>
                        </linearGradient>
                    </defs>
                </svg>
            </div>
        </div>
    </div>
</div>
<script></script><style></style>

<!--<div class="woocommerce-form-coupon-toggle">-->
<!--</div>-->

<form id="coupon-form-restyle" class="checkout_coupon woocommerce-form-coupon" method="post" style="display: none;">

    <p>If you have a coupon code, please apply it below:</p>

    <div class="row-coupon">
        <div class="coupon-row-first">
            <label for="coupon_code" class="screen-reader-text">Coupon:</label>
            <input type="text" name="coupon_code" class="input-text" placeholder="Coupon code" id="coupon_code" value="">
        </div>

        <button type="submit" id="apply-coupon-button" class="place-order button button" name="apply_coupon" value="Apply coupon">Apply</button>
    </div>


    <div class="clear"></div>
</form>


<style>

    .woocommerce {
        position: relative;
    }

    #coupon-form-restyle {
        display: inline-block!important;
        padding: 0px 45px 0px 45px;
        width: calc(50% - 10px);
        max-width: 580px;
        float: right;
    }

    #apply-coupon-button  {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1;
        background: #3C4257;
        -webkit-box-shadow: 0px -1px 1px rgba(0, 0, 0, 0.12), 0px 2px 5px rgba(0, 0, 0, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.08);
        box-shadow: 0px -1px 1px rgba(0, 0, 0, 0.12), 0px 2px 5px rgba(0, 0, 0, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.08);
        border-radius: 6px;
        font-size: 15px;
        transition: 0.3s ease;
        line-height: 25px;
        font-weight: 500;
        margin-left: 10px;
    }
    #apply-coupon-button:hover {
        background: #3c4257c4;
        transition: 0.3s ease;
    }
    .row-coupon {
        display: flex;
        align-items: center;
        flex-direction: row-reverse;
    }
    .coupon-row-first input {
        border-radius: 5px !important;
    }
    .coupon-row-first {
        flex: 1;
    }
    #apply-coupon-button {
        flex: 0 0 80px;
    }

    .woocommerce .woocommerce-message {
        position: absolute;
        right: 45px;
        top: 40px;
    }




    @media (max-width: 990px) {
        #coupon-form-restyle {
            display: inline-block!important;
            padding: 0px 45px 0px 45px;
            width: 100%;
            max-width: 100%;
            float: none;
            margin-bottom: 20px;
        }
    }

    @media (max-width: 725px) {
        .woocommerce .woocommerce-message {
            top: 15px;
        }
    }

    @media (max-width: 620px) {
        .woocommerce .woocommerce-message {
            position: relative;
            left: 0;
            right: 0;
            top: 0;
            margin-left: 50px;
        }
    }

    @media (max-width: 590px) {
        #coupon-form-restyle {
            padding: 0;
        }
        .woocommerce .woocommerce-message {
            margin-left: 0px;
        }
    }
</style>
<form name="checkout" method="post" class="checkout woocommerce-checkout" data-symbol="EUR" action="https://criminal.hyroes.com/checkout/" enctype="multipart/form-data" novalidate="novalidate">

	
		
		<div id="customer_details">

            
			<div class="billing_section">

                
				<wc-order-attribution-inputs><input type="hidden" name="wc_order_attribution_source_type" value="typein"><input type="hidden" name="wc_order_attribution_referrer" value="(none)"><input type="hidden" name="wc_order_attribution_utm_campaign" value="(none)"><input type="hidden" name="wc_order_attribution_utm_source" value="(direct)"><input type="hidden" name="wc_order_attribution_utm_medium" value="(none)"><input type="hidden" name="wc_order_attribution_utm_content" value="(none)"><input type="hidden" name="wc_order_attribution_utm_id" value="(none)"><input type="hidden" name="wc_order_attribution_utm_term" value="(none)"><input type="hidden" name="wc_order_attribution_utm_source_platform" value="(none)"><input type="hidden" name="wc_order_attribution_utm_creative_format" value="(none)"><input type="hidden" name="wc_order_attribution_utm_marketing_tactic" value="(none)"><input type="hidden" name="wc_order_attribution_session_entry" value="https://criminal.hyroes.com/"><input type="hidden" name="wc_order_attribution_session_start_time" value="2025-07-09 10:23:04"><input type="hidden" name="wc_order_attribution_session_pages" value="16"><input type="hidden" name="wc_order_attribution_session_count" value="1"><input type="hidden" name="wc_order_attribution_user_agent" value="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"></wc-order-attribution-inputs><div class="woocommerce-billing-fields">

	
	<div class="woocommerce-billing-fields__field-wrapper" style="opacity: 1;">

        
	<h3 class="form-row  wcccf_field wcccf_priority-" data-priority="">Billing Information</h3><p class="form-row form-row-wide thwcfd-required thwcfd-field-wrapper thwcfd-field-email wcccf_priority-23 validate-required validate-email" id="billing_email_field" data-priority="20"><label for="billing_email" class="required_field">Email address&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="email" class="input-text " name="billing_email" id="billing_email" placeholder="<EMAIL>" value="<EMAIL>" aria-required="true" autocomplete="email"></span></p><p class="form-row form-row-first thwcfd-required thwcfd-field-wrapper thwcfd-field-text wcccf_priority-34 validate-required" id="billing_first_name_field" data-priority="30"><label for="billing_first_name" class="required_field">First name&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_first_name" id="billing_first_name" placeholder="Enter your first name" value="" aria-required="true" autocomplete="given-name"></span></p><p class="form-row form-row-last thwcfd-required thwcfd-field-wrapper thwcfd-field-text wcccf_priority-45 validate-required" id="billing_last_name_field" data-priority="40"><label for="billing_last_name" class="required_field">Last name&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_last_name" id="billing_last_name" placeholder="Enter your last name" value="" aria-required="true" autocomplete="family-name"></span></p><p class="form-row form-row-wide address-field update_totals_on_change thwcfd-required thwcfd-field-wrapper thwcfd-field-country wcccf_priority-56 validate-required" id="billing_country_field" data-priority="50"><label for="billing_country" class="required_field">Country / Region&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><select name="billing_country" id="billing_country" class="country_to_state country_select select2-hidden-accessible" aria-required="true" autocomplete="country" data-placeholder="Select a country / region…" data-label="Country / Region" tabindex="-1" aria-hidden="true"><option value="">Select a country / region…</option><option value="AF">Afghanistan</option><option value="AX">Åland Islands</option><option value="AL">Albania</option><option value="DZ">Algeria</option><option value="AS">American Samoa</option><option value="AD">Andorra</option><option value="AO">Angola</option><option value="AI">Anguilla</option><option value="AQ">Antarctica</option><option value="AG">Antigua and Barbuda</option><option value="AR">Argentina</option><option value="AM">Armenia</option><option value="AW">Aruba</option><option value="AU">Australia</option><option value="AT">Austria</option><option value="AZ">Azerbaijan</option><option value="BS">Bahamas</option><option value="BH">Bahrain</option><option value="BD">Bangladesh</option><option value="BB">Barbados</option><option value="BY">Belarus</option><option value="PW">Belau</option><option value="BE">Belgium</option><option value="BZ">Belize</option><option value="BJ">Benin</option><option value="BM">Bermuda</option><option value="BT">Bhutan</option><option value="BO">Bolivia</option><option value="BQ">Bonaire, Saint Eustatius and Saba</option><option value="BA">Bosnia and Herzegovina</option><option value="BW">Botswana</option><option value="BV">Bouvet Island</option><option value="BR">Brazil</option><option value="IO">British Indian Ocean Territory</option><option value="BN">Brunei</option><option value="BG">Bulgaria</option><option value="BF">Burkina Faso</option><option value="BI">Burundi</option><option value="KH">Cambodia</option><option value="CM">Cameroon</option><option value="CA">Canada</option><option value="CV">Cape Verde</option><option value="KY">Cayman Islands</option><option value="CF">Central African Republic</option><option value="TD">Chad</option><option value="CL">Chile</option><option value="CN">China</option><option value="CX">Christmas Island</option><option value="CC">Cocos (Keeling) Islands</option><option value="CO">Colombia</option><option value="KM">Comoros</option><option value="CG">Congo (Brazzaville)</option><option value="CD">Congo (Kinshasa)</option><option value="CK">Cook Islands</option><option value="CR">Costa Rica</option><option value="HR">Croatia</option><option value="CU">Cuba</option><option value="CW">Curaçao</option><option value="CY">Cyprus</option><option value="CZ">Czech Republic</option><option value="DK">Denmark</option><option value="DJ">Djibouti</option><option value="DM">Dominica</option><option value="DO">Dominican Republic</option><option value="EC">Ecuador</option><option value="EG">Egypt</option><option value="SV">El Salvador</option><option value="GQ">Equatorial Guinea</option><option value="ER">Eritrea</option><option value="EE">Estonia</option><option value="SZ">Eswatini</option><option value="ET">Ethiopia</option><option value="FK">Falkland Islands</option><option value="FO">Faroe Islands</option><option value="FJ">Fiji</option><option value="FI">Finland</option><option value="FR">France</option><option value="GF">French Guiana</option><option value="PF">French Polynesia</option><option value="TF">French Southern Territories</option><option value="GA">Gabon</option><option value="GM">Gambia</option><option value="GE">Georgia</option><option value="DE">Germany</option><option value="GH">Ghana</option><option value="GI">Gibraltar</option><option value="GR">Greece</option><option value="GL">Greenland</option><option value="GD">Grenada</option><option value="GP">Guadeloupe</option><option value="GU">Guam</option><option value="GT">Guatemala</option><option value="GG">Guernsey</option><option value="GN">Guinea</option><option value="GW">Guinea-Bissau</option><option value="GY">Guyana</option><option value="HT">Haiti</option><option value="HM">Heard Island and McDonald Islands</option><option value="HN">Honduras</option><option value="HK">Hong Kong</option><option value="HU">Hungary</option><option value="IS">Iceland</option><option value="IN">India</option><option value="ID">Indonesia</option><option value="IR">Iran</option><option value="IQ">Iraq</option><option value="IE">Ireland</option><option value="IM">Isle of Man</option><option value="IL">Israel</option><option value="IT">Italy</option><option value="CI">Ivory Coast</option><option value="JM">Jamaica</option><option value="JP">Japan</option><option value="JE">Jersey</option><option value="JO">Jordan</option><option value="KZ">Kazakhstan</option><option value="KE">Kenya</option><option value="KI">Kiribati</option><option value="KW">Kuwait</option><option value="KG">Kyrgyzstan</option><option value="LA">Laos</option><option value="LV">Latvia</option><option value="LB">Lebanon</option><option value="LS">Lesotho</option><option value="LR">Liberia</option><option value="LY">Libya</option><option value="LI">Liechtenstein</option><option value="LT">Lithuania</option><option value="LU">Luxembourg</option><option value="MO">Macao</option><option value="MG">Madagascar</option><option value="MW">Malawi</option><option value="MY">Malaysia</option><option value="MV">Maldives</option><option value="ML">Mali</option><option value="MT">Malta</option><option value="MH">Marshall Islands</option><option value="MQ">Martinique</option><option value="MR">Mauritania</option><option value="MU">Mauritius</option><option value="YT">Mayotte</option><option value="MX">Mexico</option><option value="FM">Micronesia</option><option value="MD">Moldova</option><option value="MC">Monaco</option><option value="MN">Mongolia</option><option value="ME">Montenegro</option><option value="MS">Montserrat</option><option value="MA">Morocco</option><option value="MZ">Mozambique</option><option value="MM">Myanmar</option><option value="NA">Namibia</option><option value="NR">Nauru</option><option value="NP">Nepal</option><option value="NL">Netherlands</option><option value="NC">New Caledonia</option><option value="NZ">New Zealand</option><option value="NI">Nicaragua</option><option value="NE">Niger</option><option value="NG">Nigeria</option><option value="NU">Niue</option><option value="NF">Norfolk Island</option><option value="KP">North Korea</option><option value="MK">North Macedonia</option><option value="MP">Northern Mariana Islands</option><option value="NO">Norway</option><option value="OM">Oman</option><option value="PK">Pakistan</option><option value="PS">Palestinian Territory</option><option value="PA">Panama</option><option value="PG">Papua New Guinea</option><option value="PY">Paraguay</option><option value="PE">Peru</option><option value="PH">Philippines</option><option value="PN">Pitcairn</option><option value="PL">Poland</option><option value="PT">Portugal</option><option value="PR">Puerto Rico</option><option value="QA">Qatar</option><option value="RE">Reunion</option><option value="RO">Romania</option><option value="RU">Russia</option><option value="RW">Rwanda</option><option value="ST">São Tomé and Príncipe</option><option value="BL">Saint Barthélemy</option><option value="SH">Saint Helena</option><option value="KN">Saint Kitts and Nevis</option><option value="LC">Saint Lucia</option><option value="SX">Saint Martin (Dutch part)</option><option value="MF">Saint Martin (French part)</option><option value="PM">Saint Pierre and Miquelon</option><option value="VC">Saint Vincent and the Grenadines</option><option value="WS">Samoa</option><option value="SM">San Marino</option><option value="SA">Saudi Arabia</option><option value="SN">Senegal</option><option value="RS">Serbia</option><option value="SC">Seychelles</option><option value="SL">Sierra Leone</option><option value="SG">Singapore</option><option value="SK">Slovakia</option><option value="SI">Slovenia</option><option value="SB">Solomon Islands</option><option value="SO">Somalia</option><option value="ZA">South Africa</option><option value="GS">South Georgia/Sandwich Islands</option><option value="KR">South Korea</option><option value="SS">South Sudan</option><option value="ES">Spain</option><option value="LK">Sri Lanka</option><option value="SD">Sudan</option><option value="SR">Suriname</option><option value="SJ">Svalbard and Jan Mayen</option><option value="SE">Sweden</option><option value="CH">Switzerland</option><option value="SY">Syria</option><option value="TW">Taiwan</option><option value="TJ">Tajikistan</option><option value="TZ">Tanzania</option><option value="TH">Thailand</option><option value="TL">Timor-Leste</option><option value="TG">Togo</option><option value="TK">Tokelau</option><option value="TO">Tonga</option><option value="TT">Trinidad and Tobago</option><option value="TN">Tunisia</option><option value="TR">Turkey</option><option value="TM">Turkmenistan</option><option value="TC">Turks and Caicos Islands</option><option value="TV">Tuvalu</option><option value="UG">Uganda</option><option value="UA">Ukraine</option><option value="AE">United Arab Emirates</option><option value="GB">United Kingdom (UK)</option><option value="US">United States (US)</option><option value="UM">United States (US) Minor Outlying Islands</option><option value="UY">Uruguay</option><option value="UZ">Uzbekistan</option><option value="VU">Vanuatu</option><option value="VA">Vatican</option><option value="VE">Venezuela</option><option value="VN">Vietnam</option><option value="VG">Virgin Islands (British)</option><option value="VI">Virgin Islands (US)</option><option value="WF">Wallis and Futuna</option><option value="EH">Western Sahara</option><option value="YE">Yemen</option><option value="ZM">Zambia</option><option value="ZW">Zimbabwe</option></select><span class="select2 select2-container select2-container--default" dir="ltr" style="width: 100%;"><span class="selection"><span class="select2-selection select2-selection--single" aria-haspopup="true" aria-expanded="false" tabindex="0" aria-required="true" aria-label="Country / Region" role="combobox"><span class="select2-selection__rendered" aria-required="true" id="select2-billing_country-container" role="textbox" aria-readonly="true"><span class="select2-selection__placeholder">Select a country / region…</span></span><span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span></span></span><span class="dropdown-wrapper" aria-hidden="true"></span></span><noscript><button type="submit" name="woocommerce_checkout_update_totals" value="Update country / region">Update country / region</button></noscript></span></p><p class="form-row form-row-first address-field thwcfd-required thwcfd-field-wrapper thwcfd-field-text wcccf_priority-67 validate-required" id="billing_city_field" data-priority="60"><label for="billing_city" class="screen-reader-text required_field">Town/City&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_city" id="billing_city" placeholder="Town/City" value="" aria-required="true" autocomplete="address-level2"></span></p><p class="form-row form-row-last address-field thwcfd-required thwcfd-field-wrapper thwcfd-field-text wcccf_priority-78 validate-required validate-postcode" id="billing_postcode_field" data-priority="70"><label for="billing_postcode" class="screen-reader-text required_field">Postcode&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_postcode" id="billing_postcode" placeholder="Postcode" value="" aria-required="true" autocomplete="postal-code"></span></p><p class="form-row form-row-wide wcccf_hidden_field wcccf_priority-514" id="billing_wcccf_label_gPr2Pa4e0OGd2ns_field" data-priority="514"><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_wcccf_label_gPr2Pa4e0OGd2ns" id="billing_wcccf_label_gPr2Pa4e0OGd2ns" placeholder="" value="Billing Information"></span></p></div>

	
</div>

			</div>

			<div class="shipping_section">
				<div class="woocommerce-shipping-fields">
	</div>
<div class="woocommerce-additional-fields">
	
	
	<div id="wcccf_current_lang_container">
				<input type="hidden" class="input-hidden" name="wcccf_current_lang" id="wcccf_current_lang_input" value="en_US">
			</div></div>
			</div>

		</div>

		
	
	<div id="order_review" class="woocommerce-checkout-review-order">

        
        <h3 id="order_review_heading">Order Summary        <span class="product-image">
            <img fetchpriority="high" decoding="async" width="300" height="300" src="https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-300x300.png" class="attachment-woocommerce_thumbnail size-woocommerce_thumbnail lazyautosizes lazyloaded" alt="Criminal Mystery Box" data-src="https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-300x300.png" data-srcset="https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-300x300.png 300w, https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-100x100.png 100w, https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-150x150.png 150w" data-sizes="auto" data-eio-rwidth="300" data-eio-rheight="300" sizes="100px" srcset="https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-300x300.png 300w, https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-100x100.png 100w, https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-150x150.png 150w"><noscript><img fetchpriority="high" decoding="async" width="300" height="300" src="https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-300x300.png" class="attachment-woocommerce_thumbnail size-woocommerce_thumbnail" alt="Criminal Mystery Box" srcset="https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-300x300.png 300w, https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-100x100.png 100w, https://criminal.hyroes.com/wp-content/uploads/2025/04/Criminal_Box-150x150.png 150w" sizes="(max-width: 300px) 100vw, 300px" data-eio="l" /></noscript>        </span>
        </h3>

        
        <table class="shop_table woocommerce-checkout-review-order-table">

	<tbody>

        <tr>
            <td class="top-total" colspan="2"><strong><span class="woocommerce-Price-amount amount"><bdi>21,99<span class="woocommerce-Price-currencySymbol">EUR</span></bdi></span></strong> </td>
        </tr>

		        <tr class="cart_item">
            <td class="product-name">
                <div class="name">
                    1x                    Criminal Mystery Box&nbsp;                </div>
                                    <div class="product-description">
                        <ul>
 	<li><strong>50-500+ Skins or 1x OG or Exclusive Skin</strong></li>
 	<li>Random Emotes &amp; Pickaxes</li>
 	<li>Full Access &amp; Pull-Back Warranty</li>
</ul>                    </div>
                            </td>
            <td class="product-total">
                <span class="woocommerce-Price-amount amount"><bdi>21,99<span class="woocommerce-Price-currencySymbol">EUR</span></bdi></span>            </td>
        </tr>
        	</tbody>
	<tfoot>

		<tr class="cart-subtotal">
			<th>Subtotal</th>
			<td><span class="woocommerce-Price-amount amount"><bdi>21,99<span class="woocommerce-Price-currencySymbol">EUR</span></bdi></span></td>
		</tr>

		
		
		
														
		
		<tr class="order-total">
			<th>Total due</th>
			<td><strong><span class="woocommerce-Price-amount amount"><bdi>21,99<span class="woocommerce-Price-currencySymbol">EUR</span></bdi></span></strong> </td>
		</tr>

		
	</tfoot>
</table>

<div id="payment" class="woocommerce-checkout-payment">

    <h3>Choose a payment method</h3>

			<ul class="wc_payment_methods payment_methods methods" style="height: 276.666px;">
			<li class="wc_payment_method payment_method_icenox_pay_cards">

    <div class="input_box">
        <input id="payment_method_icenox_pay_cards" type="radio" class="input-radio" name="payment_method" value="icenox_pay_cards" data-order_button_text="">

        <label for="payment_method_icenox_pay_cards">
            Credit card or Debit card <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/cards.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_apple_pay">

    <div class="input_box">
        <input id="payment_method_icenox_pay_apple_pay" type="radio" class="input-radio" name="payment_method" value="icenox_pay_apple_pay" data-order_button_text="">

        <label for="payment_method_icenox_pay_apple_pay">
            Apple Pay <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/apple-pay.svg">        </label>
    </div>

			<div class="payment_box payment_method_icenox_pay_apple_pay" style="display:none;">
			Conveniently pay with Apple Pay. Use your iPhone, iPad, or any other Apple Device for a secure and seamless checkout experience.		</div>
	</li>
<li class="wc_payment_method payment_method_icenox_pay_google_pay">

    <div class="input_box">
        <input id="payment_method_icenox_pay_google_pay" type="radio" class="input-radio" name="payment_method" value="icenox_pay_google_pay" data-order_button_text="">

        <label for="payment_method_icenox_pay_google_pay">
            Google Pay <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/google-pay.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_paypal">

    <div class="input_box">
        <input id="payment_method_icenox_pay_paypal" type="radio" class="input-radio" name="payment_method" value="icenox_pay_paypal" data-order_button_text="">

        <label for="payment_method_icenox_pay_paypal">
            PayPal <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/paypal.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_amazon_pay">

    <div class="input_box">
        <input id="payment_method_icenox_pay_amazon_pay" type="radio" class="input-radio" name="payment_method" value="icenox_pay_amazon_pay" data-order_button_text="">

        <label for="payment_method_icenox_pay_amazon_pay">
            Amazon Pay <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/amazon-pay.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_ideal">

    <div class="input_box">
        <input id="payment_method_icenox_pay_ideal" type="radio" class="input-radio" name="payment_method" value="icenox_pay_ideal" data-order_button_text="">

        <label for="payment_method_icenox_pay_ideal">
            iDEAL <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/ideal.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_twint">

    <div class="input_box">
        <input id="payment_method_icenox_pay_twint" type="radio" class="input-radio" name="payment_method" value="icenox_pay_twint" data-order_button_text="">

        <label for="payment_method_icenox_pay_twint">
            TWINT <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/twint.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_bancontact">

    <div class="input_box">
        <input id="payment_method_icenox_pay_bancontact" type="radio" class="input-radio" name="payment_method" value="icenox_pay_bancontact" data-order_button_text="">

        <label for="payment_method_icenox_pay_bancontact">
            Bancontact <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/bancontact.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_blik">

    <div class="input_box">
        <input id="payment_method_icenox_pay_blik" type="radio" class="input-radio" name="payment_method" value="icenox_pay_blik" data-order_button_text="">

        <label for="payment_method_icenox_pay_blik">
            BLIK <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/blik.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_eps">

    <div class="input_box">
        <input id="payment_method_icenox_pay_eps" type="radio" class="input-radio" name="payment_method" value="icenox_pay_eps" checked="checked" data-order_button_text="">

        <label for="payment_method_icenox_pay_eps">
            EPS <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/eps.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_link">

    <div class="input_box">
        <input id="payment_method_icenox_pay_link" type="radio" class="input-radio" name="payment_method" value="icenox_pay_link" data-order_button_text="">

        <label for="payment_method_icenox_pay_link">
            Link <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/link.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_mobilepay">

    <div class="input_box">
        <input id="payment_method_icenox_pay_mobilepay" type="radio" class="input-radio" name="payment_method" value="icenox_pay_mobilepay" data-order_button_text="">

        <label for="payment_method_icenox_pay_mobilepay">
            MobilePay <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/mobilepay.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_multibanco">

    <div class="input_box">
        <input id="payment_method_icenox_pay_multibanco" type="radio" class="input-radio" name="payment_method" value="icenox_pay_multibanco" data-order_button_text="">

        <label for="payment_method_icenox_pay_multibanco">
            Multibanco <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/multibanco.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_p24">

    <div class="input_box">
        <input id="payment_method_icenox_pay_p24" type="radio" class="input-radio" name="payment_method" value="icenox_pay_p24" data-order_button_text="">

        <label for="payment_method_icenox_pay_p24">
            Przelewy24 <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/p24.svg">        </label>
    </div>

	</li>
<li class="wc_payment_method payment_method_icenox_pay_alipay">

    <div class="input_box">
        <input id="payment_method_icenox_pay_alipay" type="radio" class="input-radio" name="payment_method" value="icenox_pay_alipay" data-order_button_text="">

        <label for="payment_method_icenox_pay_alipay">
            Alipay <img class="icenox-pay-method-icon customized_payment_icon" src="https://criminal.hyroes.com/wp-content/plugins/woocommerce-icenox-pay-plugin/includes/assets/images/paymentmethods/alipay.svg">        </label>
    </div>

	</li>
		</ul>
	    <div class="see-more-block">
        <a href="#" class="see-more-payments">
            <span>See More</span><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.5956 4.74721C12.9169 4.4176 13.4378 4.4176 13.759 4.74721C14.0803 5.07682 14.0803 5.61122 13.759 5.94083L8.58118 11.253C8.2602 11.5823 7.7398 11.5823 7.41882 11.253L2.24096 5.94083C1.91968 5.61122 1.91968 5.07682 2.24096 4.74721C2.56223 4.4176 3.08312 4.4176 3.40439 4.74721L8 9.46203L12.5956 4.74721Z" fill="#635BFF"></path>
</svg>
</a>
    </div>
	<div class="form-row place-order">
		<noscript>
			Since your browser does not support JavaScript, or it is disabled, please ensure you click the <em>Update Totals</em> button before placing your order. You may be charged more than the amount stated above if you fail to do so.			<br/><button type="submit" class="button alt" name="woocommerce_checkout_update_totals" value="Update totals">Update totals</button>
		</noscript>

<!--		-->
		
		<button type="submit" class="button alt" name="woocommerce_checkout_place_order" id="place_order" value="Pay" data-value="Pay">Pay</button>
		
		<input type="hidden" id="woocommerce-process-checkout-nonce" name="woocommerce-process-checkout-nonce" value="c461b90077"><input type="hidden" name="_wp_http_referer" value="/?wc-ajax=update_order_review">	</div>

<!--    <div class="copyright-text copyright-text-bottom">-->
<!--        --><!--    </div>-->

</div>
<script>
    jQuery(document).ready(function($) {

        payment_method_box_height();

        function payment_method_box_height(count = 4){

            if($('body form.woocommerce-checkout').hasClass('see-all-active')){
                return;
            }

            var payments_methods_box_height = 0;
            $('body ul.wc_payment_methods li').each(function (i,e){

                if(count !== 0 && i > count){
                    return;
                }
                payments_methods_box_height += $(this).outerHeight() + 8;
            });

            if(payments_methods_box_height > 0){
                $('body ul.wc_payment_methods').css('height', payments_methods_box_height);
            }

        }

        $('body').on('click', '.see-all-active .see-more-payments', function(e) {
            e.preventDefault();
            console.log('click');
            $('body form.woocommerce-checkout').removeClass('see-all-active');
            payment_method_box_height();
            $('ul.wc_payment_methods').css('transition', '0.3s ease');
            $('body .wc_payment_methods li').css('display', 'none');
            $('body .wc_payment_methods li:nth-child(-n+5)').css('display', 'flex');
            $('body .wc_payment_methods li:nth-child(-n+5)').css('display', '-webkit-box');
            $('body .wc_payment_methods li:nth-child(-n+5)').css('display', '-ms-flexbox');
            $(this).find('span').text('See More');
        });

        $('body').on('click', 'form.woocommerce-checkout:not(.see-all-active) .see-more-payments', function(e) {
            e.preventDefault();
            payment_method_box_height(0);
            $('ul.wc_payment_methods').css('transition', '0.3s ease');
            $('body form.woocommerce-checkout').addClass('see-all-active');
            $('body .wc_payment_methods li').css('display', 'flex');
            $('body .wc_payment_methods li').css('display', '-webkit-box');
            $('body .wc_payment_methods li').css('display', '-ms-flexbox');
            $(this).find('span').text('See Less');
        });

    });
</script>

<script>
    jQuery(document).ready(function($) {

        payment_method_box_height();

        function payment_method_box_height(count = 4){

            if($('body form.woocommerce-checkout').hasClass('see-all-active')){
                return;
            }

            var payments_methods_box_height = 0;
            $('body ul.wc_payment_methods li').each(function (i,e){

                if(count !== 0 && i > count){
                    return;
                }
                payments_methods_box_height += $(this).outerHeight() + 8;
            });

            if(payments_methods_box_height > 0){
                $('body ul.wc_payment_methods').css('height', payments_methods_box_height);
            }

        }

        $('body').on('click', '.see-all-active .see-more-payments', function(e) {
            e.preventDefault();
            console.log('click');
            $('body form.woocommerce-checkout').removeClass('see-all-active');
            payment_method_box_height();
            $('ul.wc_payment_methods').css('transition', '0.3s ease');
            $('body .wc_payment_methods li').css('display', 'none');
            $('body .wc_payment_methods li:nth-child(-n+5)').css('display', 'flex');
            $('body .wc_payment_methods li:nth-child(-n+5)').css('display', '-webkit-box');
            $('body .wc_payment_methods li:nth-child(-n+5)').css('display', '-ms-flexbox');
            $(this).find('span').text('See More');
        });

        $('body').on('click', 'form.woocommerce-checkout:not(.see-all-active) .see-more-payments', function(e) {
            e.preventDefault();
            payment_method_box_height(0);
            $('ul.wc_payment_methods').css('transition', '0.3s ease');
            $('body form.woocommerce-checkout').addClass('see-all-active');
            $('body .wc_payment_methods li').css('display', 'flex');
            $('body .wc_payment_methods li').css('display', '-webkit-box');
            $('body .wc_payment_methods li').css('display', '-ms-flexbox');
            $(this).find('span').text('See Less');
        });

    });
</script>
    </div>

    
</form>

</div>

		
			</div>

	
</main>